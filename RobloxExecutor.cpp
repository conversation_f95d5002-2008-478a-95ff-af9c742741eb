#include "Core/ProcessInteraction/RobloxInjector.h"
#include "Core/LuaRuntime/SandboxedLuaEngine.h"
#include "Core/Logging/SecurityLogger.h"

#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

using namespace RobloxResearch::ProcessInteraction;
using namespace RobloxResearch::LuaRuntime;
using namespace RobloxResearch::Logging;

class RobloxExecutorApp {
private:
    RobloxInjector injector;
    SecurityLogger logger;
    std::string dylibPath;
    pid_t injectedPid = 0;
    
public:
    RobloxExecutorApp() {
        logger.log(SecurityLogger::LogLevel::INFO, "RobloxExecutor", "Initializing Roblox Executor");
        dylibPath = "./roblox_injection.dylib";
    }
    
    void printBanner() {
        std::cout << "\n";
        std::cout << "╔══════════════════════════════════════════════════════════════╗\n";
        std::cout << "║                    Roblox Executor v1.0                     ║\n";
        std::cout << "║                  macOS Injection Framework                   ║\n";
        std::cout << "╚══════════════════════════════════════════════════════════════╝\n";
        std::cout << "\n";
    }
    
    void printMenu() {
        std::cout << "\n=== Roblox Executor Menu ===\n";
        std::cout << "1. Find Roblox Processes\n";
        std::cout << "2. Create Injection Dylib\n";
        std::cout << "3. Inject into Roblox\n";
        std::cout << "4. Execute Lua Code\n";
        std::cout << "5. Test Print Statement\n";
        std::cout << "6. Verify Injection\n";
        std::cout << "7. Monitor Injected Process\n";
        std::cout << "8. Exit\n";
        std::cout << "Choice: ";
    }
    
    void findRobloxProcesses() {
        std::cout << "\n=== Searching for Roblox Processes ===\n";
        
        auto processes = injector.findRobloxProcesses();
        
        if (processes.empty()) {
            std::cout << "No Roblox processes found.\n";
            std::cout << "Please start Roblox and try again.\n";
            return;
        }
        
        std::cout << "Found " << processes.size() << " Roblox process(es):\n\n";
        
        for (const auto& process : processes) {
            std::cout << "PID: " << process.pid << "\n";
            std::cout << "Name: " << process.name << "\n";
            std::cout << "Bundle ID: " << process.bundleId << "\n";
            std::cout << "Executable: " << process.executablePath << "\n";
            std::cout << "Injectable: " << (process.isAccessible ? "Yes" : "No") << "\n";
            std::cout << "Loaded Libraries: " << process.loadedLibraries.size() << "\n";
            std::cout << "---\n";
        }
    }
    
    void createInjectionDylib() {
        std::cout << "\n=== Creating Injection Dylib ===\n";
        
        std::cout << "Creating dylib with Roblox API support...\n";
        
        bool success = injector.createInjectionDylib(dylibPath, true);
        
        if (success) {
            std::cout << "✓ Successfully created injection dylib: " << dylibPath << "\n";
            
            // Sign the dylib
            std::cout << "Signing dylib for injection...\n";
            if (injector.signDylibForInjection(dylibPath)) {
                std::cout << "✓ Successfully signed dylib\n";
            } else {
                std::cout << "⚠ Warning: Failed to sign dylib (may cause injection issues)\n";
            }
        } else {
            std::cout << "✗ Failed to create injection dylib\n";
            std::cout << "Make sure you have:\n";
            std::cout << "- Xcode command line tools installed\n";
            std::cout << "- LuaJIT installed (brew install luajit)\n";
        }
    }
    
    void injectIntoRoblox() {
        std::cout << "\n=== Injecting into Roblox ===\n";
        
        // Check if dylib exists
        std::ifstream dylibFile(dylibPath);
        if (!dylibFile.good()) {
            std::cout << "✗ Injection dylib not found: " << dylibPath << "\n";
            std::cout << "Please create the dylib first (option 2)\n";
            return;
        }
        
        RobloxInjector::InjectionConfig config;
        config.dylibPath = dylibPath;
        config.preferredMethod = RobloxInjector::InjectionMethod::DYLD_INSERT_LIBRARIES;
        config.autoDetectRoblox = true;
        config.verifyInjection = true;
        config.enableLogging = true;
        
        std::cout << "Attempting injection...\n";
        
        auto result = injector.injectIntoRoblox(config);
        
        switch (result.status) {
            case RobloxInjector::InjectionStatus::SUCCESS:
                std::cout << "✓ Successfully injected into Roblox!\n";
                std::cout << "Target PID: " << result.targetPid << "\n";
                std::cout << "Method: DYLD_INSERT_LIBRARIES\n";
                std::cout << "Code Execution Verified: " << (result.codeExecutionVerified ? "Yes" : "No") << "\n";
                injectedPid = result.targetPid;
                break;
                
            case RobloxInjector::InjectionStatus::PROCESS_NOT_FOUND:
                std::cout << "✗ No Roblox processes found\n";
                std::cout << "Please start Roblox and try again\n";
                break;
                
            case RobloxInjector::InjectionStatus::ACCESS_DENIED:
                std::cout << "✗ Access denied - insufficient privileges\n";
                std::cout << "Try running with sudo or disable SIP\n";
                break;
                
            case RobloxInjector::InjectionStatus::INJECTION_FAILED:
                std::cout << "✗ Injection failed: " << result.errorMessage << "\n";
                break;
                
            default:
                std::cout << "✗ Unknown injection error\n";
                break;
        }
    }
    
    void executeLuaCode() {
        std::cout << "\n=== Execute Lua Code ===\n";
        
        if (injectedPid == 0) {
            std::cout << "✗ No injected process found\n";
            std::cout << "Please inject into Roblox first (option 3)\n";
            return;
        }
        
        std::cout << "Enter Lua code to execute (empty line to finish):\n";
        std::string luaCode;
        std::string line;
        
        while (std::getline(std::cin, line) && !line.empty()) {
            luaCode += line + "\n";
        }
        
        if (luaCode.empty()) {
            std::cout << "No code entered\n";
            return;
        }
        
        std::cout << "Executing Lua code in injected process...\n";
        
        std::string result = injector.executeLuaCode(injectedPid, luaCode);
        std::cout << "Result: " << result << "\n";
    }
    
    void testPrintStatement() {
        std::cout << "\n=== Testing Print Statement ===\n";
        
        if (injectedPid == 0) {
            std::cout << "✗ No injected process found\n";
            std::cout << "Please inject into Roblox first (option 3)\n";
            return;
        }
        
        std::string testCode = "print('Hello from injected code!')";
        std::cout << "Executing test print statement...\n";
        std::cout << "Code: " << testCode << "\n";
        
        std::string result = injector.executeLuaCode(injectedPid, testCode);
        std::cout << "Result: " << result << "\n";
        
        std::cout << "\nCheck the Roblox console or terminal output for the print message.\n";
    }
    
    void verifyInjection() {
        std::cout << "\n=== Verifying Injection ===\n";
        
        if (injectedPid == 0) {
            std::cout << "✗ No injected process to verify\n";
            return;
        }
        
        bool verified = injector.verifyInjection(injectedPid, dylibPath);
        
        if (verified) {
            std::cout << "✓ Injection verified - dylib is loaded in target process\n";
        } else {
            std::cout << "✗ Injection verification failed - dylib may not be loaded\n";
        }
    }
    
    void monitorInjectedProcess() {
        std::cout << "\n=== Monitoring Injected Process ===\n";
        
        if (injectedPid == 0) {
            std::cout << "✗ No injected process to monitor\n";
            return;
        }
        
        std::cout << "Monitoring process " << injectedPid << " for 10 seconds...\n";
        
        auto issues = injector.monitorInjectedProcess(injectedPid, std::chrono::seconds(10));
        
        if (issues.empty()) {
            std::cout << "✓ No issues detected during monitoring\n";
        } else {
            std::cout << "⚠ Issues detected:\n";
            for (const auto& issue : issues) {
                std::cout << "- " << issue << "\n";
            }
        }
    }
    
    void run() {
        printBanner();
        
        int choice;
        while (true) {
            printMenu();
            std::cin >> choice;
            std::cin.ignore(); // Clear input buffer
            
            switch (choice) {
                case 1:
                    findRobloxProcesses();
                    break;
                case 2:
                    createInjectionDylib();
                    break;
                case 3:
                    injectIntoRoblox();
                    break;
                case 4:
                    executeLuaCode();
                    break;
                case 5:
                    testPrintStatement();
                    break;
                case 6:
                    verifyInjection();
                    break;
                case 7:
                    monitorInjectedProcess();
                    break;
                case 8:
                    std::cout << "Exiting Roblox Executor...\n";
                    return;
                default:
                    std::cout << "Invalid choice. Please try again.\n";
                    break;
            }
            
            std::cout << "\nPress Enter to continue...";
            std::cin.get();
        }
    }
};

int main() {
    try {
        RobloxExecutorApp app;
        app.run();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
