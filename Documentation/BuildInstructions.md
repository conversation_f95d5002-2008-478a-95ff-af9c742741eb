# Build Instructions: Roblox Executor Security Research Framework

## Prerequisites

### System Requirements
- macOS 11.0 (Big Sur) or later
- Xcode 13.0 or later
- Command Line Tools for Xcode
- System Integrity Protection (SIP) **ENABLED** (required for valid research)

### Dependencies
- **LuaJIT**: Lua runtime for sandboxed script execution
- **CMake**: Build system for C++ components
- **Homebrew**: Package manager for dependencies (recommended)

## Installation Steps

### 1. Install Dependencies

#### Using Homebrew (Recommended)
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required packages
brew install luajit cmake pkg-config

# Verify installations
luajit -v
cmake --version
```

#### Manual Installation (Alternative)
```bash
# Download and build Lua<PERSON><PERSON> manually
git clone https://github.com/LuaJIT/LuaJIT.git
cd LuaJIT
make && sudo make install
```

### 2. Verify System Configuration

#### Check SIP Status (CRITICAL)
```bash
csrutil status
# Expected output: "System Integrity Protection status: enabled."
# If disabled, enable it before proceeding with research
```

#### Check Code Signing Tools
```bash
codesign --version
# Verify code signing tools are available
```

### 3. Clone and Build Project

#### Clone Repository
```bash
git clone <repository-url>
cd RobloxExecutorResearch
```

#### Build with CMake
```bash
# Create build directory
mkdir build
cd build

# Configure build
cmake .. -DCMAKE_BUILD_TYPE=Debug

# Build project
make -j$(sysctl -n hw.ncpu)

# Run security validation
make security_check
```

#### Build with Xcode
```bash
# Open Xcode project
open RobloxExecutorResearch.xcodeproj

# Or build from command line
xcodebuild -project RobloxExecutorResearch.xcodeproj -scheme RobloxExecutorResearch -configuration Debug build
```

### 4. Verify Build

#### Run Tests
```bash
# From build directory
./ResearchTests

# Expected output should show:
# - Security compliance validation
# - Process analysis tests
# - Lua runtime tests
# - Memory analysis tests
```

#### Check Security Compliance
```bash
# Verify code signing
codesign --verify --deep --strict ./ResearchTests

# Check entitlements (should be minimal for research)
codesign -d --entitlements - ./ResearchTests
```

## Configuration

### 1. Security Settings

#### Required Security Configuration
- **SIP**: Must be enabled (`csrutil status` shows enabled)
- **Gatekeeper**: Should be enabled (`spctl --status`)
- **Code Signing**: All binaries must be properly signed

#### Optional Security Hardening
```bash
# Enable additional security features
sudo spctl --master-enable
sudo defaults write /Library/Preferences/com.apple.security GKAutoRearm -bool YES
```

### 2. Development Environment

#### Xcode Configuration
1. Open `RobloxExecutorResearch.xcodeproj`
2. Select appropriate development team for code signing
3. Ensure deployment target is set to macOS 11.0+
4. Verify all frameworks are properly linked

#### CMake Configuration Options
```bash
# Debug build with additional security checks
cmake .. -DCMAKE_BUILD_TYPE=Debug -DENABLE_SECURITY_CHECKS=ON

# Release build with optimizations
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0

# Universal binary for both Intel and Apple Silicon
cmake .. -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64"
```

## Running the Research Framework

### 1. Command Line Interface
```bash
# Run basic research tests
./ResearchTests

# Run with verbose output
./ResearchTests --verbose

# Run specific test categories
./ResearchTests --process-analysis
./ResearchTests --lua-runtime
./ResearchTests --memory-analysis
```

### 2. GUI Application (if built)
```bash
# Launch GUI research interface
open RobloxExecutorResearch.app

# Or from command line
./RobloxExecutorResearch
```

## Troubleshooting

### Common Build Issues

#### LuaJIT Not Found
```bash
# Error: Could not find LuaJIT
# Solution: Install via Homebrew or set paths manually
export LUAJIT_INCLUDE_DIR=/usr/local/include/luajit-2.1
export LUAJIT_LIBRARY=/usr/local/lib/libluajit-5.1.dylib
```

#### Code Signing Failures
```bash
# Error: Code signing failed
# Solution: Configure development team in Xcode or sign manually
codesign --force --sign "Developer ID Application: Your Name" ./ResearchTests
```

#### SIP Disabled Warning
```bash
# Warning: SIP is disabled
# Solution: Enable SIP for valid research
csrutil enable
# Reboot required after enabling SIP
```

### Runtime Issues

#### Permission Denied Errors
- **Cause**: Insufficient permissions for process analysis
- **Solution**: This is expected behavior - demonstrates security effectiveness
- **Note**: Framework is designed to work within security constraints

#### Lua Sandbox Violations
- **Cause**: Script attempting to access restricted functions
- **Solution**: This is expected - demonstrates sandbox effectiveness
- **Note**: Review script for dangerous function calls

#### Memory Analysis Limitations
- **Cause**: Security restrictions prevent memory access
- **Solution**: This is expected - demonstrates memory protection
- **Note**: Use legitimate analysis methods only

## Development Guidelines

### 1. Security-First Development
- Always maintain SIP enabled during development
- Use only documented, legitimate APIs
- Implement proper error handling for security failures
- Document security limitations and their rationale

### 2. Code Quality Standards
```bash
# Format code consistently
clang-format -i **/*.cpp **/*.h

# Run static analysis
clang-static-analyzer **/*.cpp

# Check for security issues
scan-build make
```

### 3. Testing Requirements
- All security mechanisms must be tested
- Verify framework operates within constraints
- Document expected failures and limitations
- Test on both Intel and Apple Silicon Macs

## Deployment Considerations

### 1. Distribution
- Code must be properly signed for distribution
- Include security compliance documentation
- Provide clear usage guidelines
- Document research limitations

### 2. Security Validation
```bash
# Validate security compliance before distribution
./validate_security_compliance.sh

# Check for prohibited techniques
./check_security_violations.sh

# Generate compliance report
./generate_compliance_report.sh
```

## Support and Documentation

### Additional Resources
- `Documentation/TechnicalFindings.md` - Detailed research findings
- `Documentation/SecurityAnalysis.md` - Security mechanism analysis
- `Documentation/APIReference.md` - Framework API documentation
- `Tests/` - Example usage and test cases

### Getting Help
1. Review documentation thoroughly
2. Check that SIP is enabled and system is properly configured
3. Verify all dependencies are correctly installed
4. Ensure code signing is working properly

---

**Important**: This framework is designed for legitimate security research within macOS security constraints. It demonstrates why traditional executor techniques fail on modern macOS rather than attempting to bypass security mechanisms.
