#import "AdvancedScriptEditor.h"
#include "../Core/Logging/SecurityLogger.h"

@implementation AdvancedScriptEditor

- (instancetype)initWithFrame:(NSRect)frameRect {
    self = [super initWithFrame:frameRect];
    if (self) {
        [self setupAdvancedFeatures];
    }
    return self;
}

- (void)setupAdvancedFeatures {
    // Initialize properties
    self.syntaxHighlightingEnabled = YES;
    self.realTimeValidationEnabled = YES;
    self.showLineNumbers = YES;
    
    // Setup syntax colors
    self.syntaxColors = [[NSMutableDictionary alloc] init];
    [self setupDefaultColors];
    
    // Setup Lua keywords and Roblox API
    [self setupLuaKeywords];
    [self setupRobloxAPI];
    
    // Initialize collections
    self.errorRanges = [[NSMutableArray alloc] init];
    self.securityWarnings = [[NSMutableArray alloc] init];
    
    // Setup advanced text features
    [self setupAdvancedTextFeatures];
    
    // Setup code completion
    [self setupCodeCompletion];
    
    // Setup line numbers
    if (self.showLineNumbers) {
        [self enableLineNumbers:YES];
    }
    
    // Setup real-time validation
    if (self.realTimeValidationEnabled) {
        [self enableRealTimeValidation:YES];
    }
    
    // Load script templates
    [self loadScriptTemplates];
    
    // Set delegate
    self.delegate = self;
    
    NSLog(@"[EDUCATIONAL] Advanced Script Editor initialized with security features");
}

- (void)setupDefaultColors {
    self.syntaxColors[@"keyword"] = [NSColor colorWithRed:0.0 green:0.0 blue:1.0 alpha:1.0]; // Blue
    self.syntaxColors[@"string"] = [NSColor colorWithRed:0.0 green:0.6 blue:0.0 alpha:1.0]; // Green
    self.syntaxColors[@"comment"] = [NSColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0]; // Gray
    self.syntaxColors[@"number"] = [NSColor colorWithRed:0.8 green:0.4 blue:0.0 alpha:1.0]; // Orange
    self.syntaxColors[@"roblox_api"] = [NSColor colorWithRed:0.6 green:0.0 blue:0.6 alpha:1.0]; // Purple
    self.errorHighlightColor = [NSColor colorWithRed:1.0 green:0.0 blue:0.0 alpha:0.3]; // Red with transparency
}

- (void)setupLuaKeywords {
    self.luaKeywords = @[
        @"and", @"break", @"do", @"else", @"elseif", @"end", @"false", @"for",
        @"function", @"if", @"in", @"local", @"nil", @"not", @"or", @"repeat",
        @"return", @"then", @"true", @"until", @"while", @"goto"
    ];
}

- (void)setupRobloxAPI {
    // Educational Roblox API simulation
    self.robloxAPI = @[
        @"game", @"workspace", @"Players", @"ReplicatedStorage", @"ServerStorage",
        @"StarterGui", @"StarterPack", @"StarterPlayer", @"Lighting", @"SoundService",
        @"TweenService", @"RunService", @"UserInputService", @"ContextActionService",
        @"print", @"warn", @"error", @"wait", @"spawn", @"delay", @"tick", @"time",
        @"Instance.new", @"Vector3.new", @"CFrame.new", @"Color3.new", @"UDim2.new"
    ];
}

- (void)setupAdvancedTextFeatures {
    // Enable rich text
    [self setRichText:YES];
    [self setImportsGraphics:NO];
    [self setUsesFindPanel:YES];
    [self setUsesRuler:NO];
    
    // Setup font
    NSFont *codeFont = [NSFont fontWithName:@"SF Mono" size:14.0];
    if (!codeFont) {
        codeFont = [NSFont fontWithName:@"Monaco" size:14.0];
    }
    if (!codeFont) {
        codeFont = [NSFont monospacedSystemFontOfSize:14.0 weight:NSFontWeightRegular];
    }
    [self setFont:codeFont];
    
    // Setup text container
    NSTextContainer *container = [self textContainer];
    [container setWidthTracksTextView:YES];
    [container setContainerSize:NSMakeSize(FLT_MAX, FLT_MAX)];
    
    // Enable automatic features
    [self setAutomaticQuoteSubstitutionEnabled:NO];
    [self setAutomaticDashSubstitutionEnabled:NO];
    [self setAutomaticTextReplacementEnabled:NO];
    [self setAutomaticSpellingCorrectionEnabled:NO];
    [self setContinuousSpellCheckingEnabled:NO];
    
    // Setup tab stops for code indentation
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    NSMutableArray *tabStops = [[NSMutableArray alloc] init];
    for (int i = 1; i <= 50; i++) {
        NSTextTab *tab = [[NSTextTab alloc] initWithType:NSLeftTabStopType location:i * 28.0];
        [tabStops addObject:tab];
    }
    [paragraphStyle setTabStops:tabStops];
    [self setDefaultParagraphStyle:paragraphStyle];
}

- (void)setupCodeCompletion {
    // Create completion popover
    self.completionPopover = [[NSPopover alloc] init];
    self.completionPopover.behavior = NSPopoverBehaviorTransient;
    
    // Create completion table view
    NSScrollView *scrollView = [[NSScrollView alloc] initWithFrame:NSMakeRect(0, 0, 300, 200)];
    self.completionTableView = [[NSTableView alloc] initWithFrame:scrollView.bounds];
    
    NSTableColumn *column = [[NSTableColumn alloc] initWithIdentifier:@"completion"];
    column.title = @"Completions";
    column.width = 280;
    [self.completionTableView addTableColumn:column];
    
    [scrollView setDocumentView:self.completionTableView];
    [scrollView setHasVerticalScroller:YES];
    
    NSViewController *controller = [[NSViewController alloc] init];
    controller.view = scrollView;
    self.completionPopover.contentViewController = controller;
    
    // Setup data source
    CodeCompletionDataSource *dataSource = [[CodeCompletionDataSource alloc] initWithEditor:self];
    self.completionTableView.dataSource = dataSource;
    self.completionTableView.delegate = dataSource;
}

- (void)enableSyntaxHighlighting:(BOOL)enabled {
    self.syntaxHighlightingEnabled = enabled;
    if (enabled) {
        [self scheduleHighlighting];
    } else {
        [self.highlightingTimer invalidate];
        self.highlightingTimer = nil;
    }
}

- (void)scheduleHighlighting {
    [self.highlightingTimer invalidate];
    self.highlightingTimer = [NSTimer scheduledTimerWithTimeInterval:0.5
                                                              target:self
                                                            selector:@selector(highlightSyntax)
                                                            userInfo:nil
                                                             repeats:NO];
}

- (void)highlightSyntax {
    if (!self.syntaxHighlightingEnabled) return;
    
    NSString *text = [self string];
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:text];
    
    // Reset to default attributes
    NSFont *font = [self font];
    [attributedText addAttribute:NSFontAttributeName value:font range:NSMakeRange(0, text.length)];
    [attributedText addAttribute:NSForegroundColorAttributeName 
                           value:[NSColor textColor] 
                           range:NSMakeRange(0, text.length)];
    
    // Highlight different syntax elements
    [self highlightKeywordsInText:attributedText];
    [self highlightStringsInText:attributedText];
    [self highlightCommentsInText:attributedText];
    [self highlightNumbersInText:attributedText];
    [self highlightRobloxAPIInText:attributedText];
    
    // Apply the highlighting
    [[self textStorage] setAttributedString:attributedText];
}

- (void)highlightKeywordsInText:(NSMutableAttributedString *)text {
    NSString *string = [text string];
    NSColor *keywordColor = self.syntaxColors[@"keyword"];
    
    for (NSString *keyword in self.luaKeywords) {
        NSString *pattern = [NSString stringWithFormat:@"\\b%@\\b", keyword];
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern
                                                                               options:0
                                                                                 error:nil];
        NSArray *matches = [regex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
        
        for (NSTextCheckingResult *match in matches) {
            [text addAttribute:NSForegroundColorAttributeName value:keywordColor range:match.range];
        }
    }
}

- (void)highlightStringsInText:(NSMutableAttributedString *)text {
    NSString *string = [text string];
    NSColor *stringColor = self.syntaxColors[@"string"];
    
    // Match single and double quoted strings
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"([\"'])(?:(?=(\\\\?))\\2.)*?\\1"
                                                                           options:0
                                                                             error:nil];
    NSArray *matches = [regex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    
    for (NSTextCheckingResult *match in matches) {
        [text addAttribute:NSForegroundColorAttributeName value:stringColor range:match.range];
    }
}

- (void)highlightCommentsInText:(NSMutableAttributedString *)text {
    NSString *string = [text string];
    NSColor *commentColor = self.syntaxColors[@"comment"];
    
    // Match single line comments (--) and multi-line comments (--[[ ]])
    NSRegularExpression *singleLineRegex = [NSRegularExpression regularExpressionWithPattern:@"--.*$"
                                                                                     options:NSRegularExpressionAnchorsMatchLines
                                                                                       error:nil];
    NSArray *singleLineMatches = [singleLineRegex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    
    for (NSTextCheckingResult *match in singleLineMatches) {
        [text addAttribute:NSForegroundColorAttributeName value:commentColor range:match.range];
    }
    
    // Multi-line comments
    NSRegularExpression *multiLineRegex = [NSRegularExpression regularExpressionWithPattern:@"--\\[\\[.*?\\]\\]"
                                                                                    options:NSRegularExpressionDotMatchesLineSeparators
                                                                                      error:nil];
    NSArray *multiLineMatches = [multiLineRegex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    
    for (NSTextCheckingResult *match in multiLineMatches) {
        [text addAttribute:NSForegroundColorAttributeName value:commentColor range:match.range];
    }
}

- (void)highlightNumbersInText:(NSMutableAttributedString *)text {
    NSString *string = [text string];
    NSColor *numberColor = self.syntaxColors[@"number"];
    
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\b\\d+(\\.\\d+)?\\b"
                                                                           options:0
                                                                             error:nil];
    NSArray *matches = [regex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    
    for (NSTextCheckingResult *match in matches) {
        [text addAttribute:NSForegroundColorAttributeName value:numberColor range:match.range];
    }
}

- (void)highlightRobloxAPIInText:(NSMutableAttributedString *)text {
    NSString *string = [text string];
    NSColor *apiColor = self.syntaxColors[@"roblox_api"];
    
    for (NSString *apiCall in self.robloxAPI) {
        NSString *pattern = [NSString stringWithFormat:@"\\b%@\\b", [NSRegularExpression escapedPatternForString:apiCall]];
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern
                                                                               options:0
                                                                                 error:nil];
        NSArray *matches = [regex matchesInString:string options:0 range:NSMakeRange(0, string.length)];
        
        for (NSTextCheckingResult *match in matches) {
            [text addAttribute:NSForegroundColorAttributeName value:apiColor range:match.range];
        }
    }
}

- (void)enableRealTimeValidation:(BOOL)enabled {
    self.realTimeValidationEnabled = enabled;
    if (enabled) {
        // Initialize Lua engine for validation
        if (!self.luaEngine) {
            self.luaEngine = new RobloxResearch::LuaRuntime::SandboxedLuaEngine();
        }
    } else {
        [self.validationTimer invalidate];
        self.validationTimer = nil;
    }
}

- (void)validateScriptSecurity {
    if (!self.realTimeValidationEnabled || !self.luaEngine) return;
    
    NSString *script = [self string];
    std::string cppScript = [script UTF8String];
    
    // Validate script security
    auto warnings = self.luaEngine->validateScriptSecurity(cppScript);
    
    NSMutableArray *nsWarnings = [[NSMutableArray alloc] init];
    for (const auto& warning : warnings) {
        [nsWarnings addObject:[NSString stringWithUTF8String:warning.c_str()]];
    }
    
    [self displaySecurityWarnings:nsWarnings];
}

- (void)displaySecurityWarnings:(NSArray *)warnings {
    [self clearSecurityWarnings];
    self.securityWarnings = [warnings mutableCopy];
    
    // Display warnings in the UI (this would be connected to a warning panel)
    if (warnings.count > 0) {
        NSLog(@"[EDUCATIONAL] Security warnings detected: %lu", (unsigned long)warnings.count);
        for (NSString *warning in warnings) {
            NSLog(@"[EDUCATIONAL] Warning: %@", warning);
        }
    }
}

- (void)loadScriptTemplates {
    ScriptTemplateManager *manager = [ScriptTemplateManager sharedManager];
    [manager loadDefaultTemplates];
    self.scriptTemplates = [manager getTemplateNames];
}

// NSTextViewDelegate methods
- (void)textDidChange:(NSNotification *)notification {
    if (self.syntaxHighlightingEnabled) {
        [self scheduleHighlighting];
    }
    
    if (self.realTimeValidationEnabled) {
        [self.validationTimer invalidate];
        self.validationTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                                target:self
                                                              selector:@selector(validateScriptSecurity)
                                                              userInfo:nil
                                                               repeats:NO];
    }
}

- (BOOL)textView:(NSTextView *)textView doCommandBySelector:(SEL)commandSelector {
    // Handle special key combinations
    if (commandSelector == @selector(insertTab:)) {
        // Custom tab handling for code indentation
        [self insertText:@"    "]; // 4 spaces instead of tab
        return YES;
    }
    
    return NO;
}

@end

// Code Completion Data Source Implementation
@implementation CodeCompletionDataSource

- (instancetype)initWithEditor:(AdvancedScriptEditor *)editor {
    self = [super init];
    if (self) {
        self.editor = editor;
        self.completions = @[];
    }
    return self;
}

- (void)updateCompletions:(NSArray *)completions {
    self.completions = completions;
    [self.editor.completionTableView reloadData];
}

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.completions.count;
}

- (id)tableView:(NSTableView *)tableView objectValueForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    if (row < self.completions.count) {
        return self.completions[row];
    }
    return @"";
}

- (void)tableViewSelectionDidChange:(NSNotification *)notification {
    NSInteger selectedRow = self.editor.completionTableView.selectedRow;
    if (selectedRow >= 0 && selectedRow < self.completions.count) {
        NSString *completion = self.completions[selectedRow];
        NSRange currentWordRange = [self.editor getCurrentWordRange];
        [self.editor insertCompletion:completion atRange:currentWordRange];
        [self.editor hideCompletionPopover];
    }
}

@end

// Script Template Manager Implementation
@implementation ScriptTemplateManager

+ (instancetype)sharedManager {
    static ScriptTemplateManager *sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sharedInstance = [[ScriptTemplateManager alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.templates = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (void)loadDefaultTemplates {
    // Basic Lua script template
    [self addTemplate:@"Basic Script" content:@"-- Educational Lua Script\n-- This demonstrates basic Lua syntax in a secure environment\n\nlocal function main()\n    print(\"Hello from educational script!\")\n    print(\"This runs in a sandboxed environment\")\nend\n\nmain()"];

    // Educational Roblox simulation template
    [self addTemplate:@"Roblox Simulation" content:@"-- Educational Roblox API Simulation\n-- This demonstrates Roblox-like APIs in a safe environment\n\n-- Simulated game object\nlocal game = {\n    Workspace = {},\n    Players = {},\n    ReplicatedStorage = {}\n}\n\n-- Educational function\nlocal function educationalDemo()\n    print(\"Educational Roblox simulation\")\n    print(\"This demonstrates API structure without actual game interaction\")\nend\n\neducationalDemo()"];

    // Security research template
    [self addTemplate:@"Security Research" content:@"-- Security Research Template\n-- This template demonstrates security boundaries\n\nlocal function securityDemo()\n    print(\"Security Research Demo\")\n    print(\"Demonstrating sandbox limitations:\")\n    \n    -- These would fail in the sandbox\n    -- os.execute() -- BLOCKED\n    -- io.open() -- BLOCKED\n    -- require() -- BLOCKED\n    \n    print(\"All dangerous functions are properly blocked\")\nend\n\nsecurityDemo()"];

    NSLog(@"[EDUCATIONAL] Script templates loaded");
}

- (NSString *)getTemplate:(NSString *)name {
    return self.templates[name];
}

- (NSArray *)getTemplateNames {
    return [self.templates allKeys];
}

- (void)addTemplate:(NSString *)name content:(NSString *)content {
    self.templates[name] = content;
}

@end
