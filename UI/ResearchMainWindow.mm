#import "ResearchMainWindow.h"
#import "AdvancedScriptEditor.h"
#include <iostream>
#include <sstream>
#include "../Core/Logging/SecurityLogger.h"

@implementation ResearchMainWindow

- (instancetype)initWithWindowNibName:(NSString *)windowNibName {
    self = [super initWithWindowNibName:windowNibName];
    if (self) {
        _processData = [[NSMutableArray alloc] init];
        [self setupResearchFramework];
    }
    return self;
}

- (void)windowDidLoad {
    [super windowDidLoad];
    [self setupUI];
    [self validateSecurityCompliance];
    [self refreshProcessList:nil];
}

- (void)setupUI {
    // Configure window
    self.window.title = @"Roblox Executor Security Research Framework";
    self.window.delegate = self;
    
    // Setup script editor
    if (self.scriptEditor) {
        self.scriptEditor.delegate = self;
        self.scriptEditor.font = [NSFont fontWithName:@"Monaco" size:12];
        self.scriptEditor.string = @"-- Lua Script Research Environment\n-- This is a sandboxed environment for security research\n\nprint(\"Hello from secure Lua sandbox!\")\nlocal result = math.sqrt(25)\nprint(\"Square root of 25 is: \" .. result)";
        
        // Enable syntax highlighting if available
        if ([self.scriptEditor respondsToSelector:@selector(setupSyntaxHighlighting)]) {
            [(ScriptEditorTextView *)self.scriptEditor setupSyntaxHighlighting];
        }
    }
    
    // Setup output console
    if (self.outputConsole) {
        self.outputConsole.editable = NO;
        self.outputConsole.font = [NSFont fontWithName:@"Monaco" size:11];
        self.outputConsole.backgroundColor = [NSColor colorWithRed:0.1 green:0.1 blue:0.1 alpha:1.0];
        self.outputConsole.textColor = [NSColor greenColor];
    }
    
    // Setup process table
    if (self.processTable) {
        ProcessTableDataSource *dataSource = [[ProcessTableDataSource alloc] initWithMainWindow:self];
        self.processTable.dataSource = dataSource;
        self.processTable.delegate = dataSource;
    }
    
    // Setup security analysis view
    if (self.securityAnalysis) {
        self.securityAnalysis.editable = NO;
        self.securityAnalysis.font = [NSFont fontWithName:@"Monaco" size:11];
    }
    
    // Initial status
    [self updateStatus:@"Research framework initialized - Security compliance validated"];
}

- (void)setupResearchFramework {
    try {
        // Initialize research components
        _processAnalyzer = new RobloxResearch::ProcessMonitor::ProcessAnalyzer();
        _luaEngine = new RobloxResearch::LuaRuntime::SandboxedLuaEngine();
        _memoryAnalyzer = new RobloxResearch::SecurityAnalysis::MemoryAnalyzer();
        _codeSigningAnalyzer = new RobloxResearch::SecurityAnalysis::CodeSigningAnalyzer();

        // Initialize enhanced components
        _robloxAnalyzer = new RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer();
        _dylibFramework = new RobloxResearch::ProcessInteraction::EducationalDylibFramework();

        // Initialize security event log
        _securityEventLog = [[NSMutableArray alloc] init];

        NSLog(@"[EDUCATIONAL] Enhanced research framework components initialized successfully");
    } catch (const std::exception& e) {
        NSLog(@"Error initializing research framework: %s", e.what());
        [self showSecurityWarning:[NSString stringWithFormat:@"Framework initialization error: %s", e.what()]];
    }
}

- (void)dealloc {
    delete _processAnalyzer;
    delete _luaEngine;
    delete _memoryAnalyzer;
}

#pragma mark - Script Execution

- (IBAction)executeScript:(id)sender {
    if (!self.scriptEditor || !_luaEngine) {
        [self showSecurityWarning:@"Script execution not available"];
        return;
    }
    
    [self updateStatus:@"Executing script in sandboxed environment..."];
    [self.progressIndicator startAnimation:nil];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        std::string script = [self.scriptEditor.string UTF8String];
        
        // Setup execution context with security constraints
        RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionContext context;
        context.script = script;
        context.timeout = std::chrono::milliseconds(5000);
        context.memoryLimit = 10 * 1024 * 1024; // 10MB limit
        context.allowFileAccess = false;
        context.allowNetworkAccess = false;
        context.allowSystemCalls = false;
        
        auto report = self->_luaEngine->executeScript(context);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.progressIndicator stopAnimation:nil];
            [self displayExecutionReport:report];
        });
    });
}

- (void)displayExecutionReport:(const RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionReport&)report {
    NSMutableString *output = [[NSMutableString alloc] init];
    
    [output appendString:@"=== Script Execution Report ===\n"];
    
    // Execution result
    switch (report.result) {
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::Success:
            [output appendString:@"Result: SUCCESS\n"];
            [self updateStatus:@"Script executed successfully"];
            break;
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::SyntaxError:
            [output appendString:@"Result: SYNTAX ERROR\n"];
            [self updateStatus:@"Script execution failed - syntax error"];
            break;
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::RuntimeError:
            [output appendString:@"Result: RUNTIME ERROR\n"];
            [self updateStatus:@"Script execution failed - runtime error"];
            break;
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::TimeoutError:
            [output appendString:@"Result: TIMEOUT\n"];
            [self updateStatus:@"Script execution timed out"];
            break;
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::SecurityViolation:
            [output appendString:@"Result: SECURITY VIOLATION\n"];
            [self updateStatus:@"Script blocked - security violation detected"];
            break;
        case RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionResult::ResourceLimitExceeded:
            [output appendString:@"Result: RESOURCE LIMIT EXCEEDED\n"];
            [self updateStatus:@"Script stopped - resource limits exceeded"];
            break;
    }
    
    // Execution details
    [output appendFormat:@"Execution Time: %lld ms\n", report.executionTime.count()];
    [output appendFormat:@"Memory Used: %zu bytes\n", report.memoryUsed];
    [output appendFormat:@"API Calls: %zu\n", report.apiCalls.size()];
    
    // Output
    if (!report.output.empty()) {
        [output appendString:@"\n--- Script Output ---\n"];
        [output appendString:[NSString stringWithUTF8String:report.output.c_str()]];
    }
    
    // Error message
    if (!report.errorMessage.empty()) {
        [output appendString:@"\n--- Error Details ---\n"];
        [output appendString:[NSString stringWithUTF8String:report.errorMessage.c_str()]];
    }
    
    // Security violations
    if (!report.securityViolations.empty()) {
        [output appendString:@"\n--- Security Violations ---\n"];
        for (const auto& violation : report.securityViolations) {
            [output appendFormat:@"• %s\n", violation.c_str()];
        }
    }
    
    // API calls
    if (!report.apiCalls.empty()) {
        [output appendString:@"\n--- API Calls Made ---\n"];
        for (const auto& call : report.apiCalls) {
            [output appendFormat:@"• %s\n", call.c_str()];
        }
    }
    
    [output appendString:@"\n"];
    [self appendToOutput:output];
}

- (IBAction)clearOutput:(id)sender {
    if (self.outputConsole) {
        self.outputConsole.string = @"";
    }
}

- (IBAction)loadSampleScript:(id)sender {
    NSString *sampleScript = @"-- Sample Security Research Script\n"
                            @"-- This demonstrates safe Lua execution in a sandboxed environment\n\n"
                            @"print(\"=== Roblox Security Research Demo ===\")\n\n"
                            @"-- Math operations (allowed)\n"
                            @"local numbers = {1, 4, 9, 16, 25}\n"
                            @"print(\"Square roots:\")\n"
                            @"for i, num in ipairs(numbers) do\n"
                            @"    print(string.format(\"sqrt(%d) = %.2f\", num, math.sqrt(num)))\n"
                            @"end\n\n"
                            @"-- String manipulation (allowed)\n"
                            @"local message = \"Security research is important\"\n"
                            @"print(\"\\nMessage: \" .. string.upper(message))\n"
                            @"print(\"Length: \" .. string.len(message))\n\n"
                            @"-- Table operations (allowed)\n"
                            @"local data = {name = \"Research\", version = 1.0, secure = true}\n"
                            @"print(\"\\nData table:\")\n"
                            @"for k, v in pairs(data) do\n"
                            @"    print(k .. \": \" .. tostring(v))\n"
                            @"end\n\n"
                            @"print(\"\\n=== Demo Complete ===\")";
    
    if (self.scriptEditor) {
        self.scriptEditor.string = sampleScript;
    }
}

#pragma mark - Process Analysis

- (IBAction)refreshProcessList:(id)sender {
    if (!_processAnalyzer) {
        [self showSecurityWarning:@"Process analyzer not available"];
        return;
    }
    
    [self updateStatus:@"Refreshing process list..."];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        auto processes = self->_processAnalyzer->getRunningProcesses();
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateProcessTableWithProcesses:processes];
            [self updateStatus:[NSString stringWithFormat:@"Found %zu processes", processes.size()]];
        });
    });
}

- (void)updateProcessTableWithProcesses:(const std::vector<RobloxResearch::ProcessMonitor::ProcessAnalyzer::ProcessInfo>&)processes {
    [self.processData removeAllObjects];
    
    for (const auto& proc : processes) {
        NSDictionary *processDict = @{
            @"pid": @(proc.pid),
            @"name": [NSString stringWithUTF8String:proc.name.c_str()],
            @"isRoblox": @(proc.isRobloxProcess),
            @"hasRestrictions": @(proc.hasSecurityRestrictions),
            @"architecture": [NSString stringWithUTF8String:proc.architecture.c_str()]
        };
        [self.processData addObject:processDict];
    }
    
    [self.processTable reloadData];
}

- (IBAction)analyzeSelectedProcess:(id)sender {
    NSInteger selectedRow = self.processTable.selectedRow;
    if (selectedRow < 0 || selectedRow >= self.processData.count) {
        [self showSecurityWarning:@"Please select a process to analyze"];
        return;
    }
    
    NSDictionary *processDict = self.processData[selectedRow];
    pid_t pid = [processDict[@"pid"] intValue];
    
    [self updateStatus:@"Analyzing process security constraints..."];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        auto constraints = self->_processAnalyzer->analyzeSecurityConstraints(pid);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [self displayProcessAnalysis:constraints forProcess:pid];
        });
    });
}

- (void)displayProcessAnalysis:(const RobloxResearch::ProcessMonitor::ProcessAnalyzer::SecurityConstraints&)constraints 
                    forProcess:(pid_t)pid {
    NSMutableString *analysis = [[NSMutableString alloc] init];
    
    [analysis appendFormat:@"=== Process Analysis (PID: %d) ===\n\n", pid];
    
    [analysis appendFormat:@"SIP Enabled: %s\n", constraints.sipEnabled ? "YES" : "NO"];
    [analysis appendFormat:@"Code Signed: %s\n", constraints.isCodeSigned ? "YES" : "NO"];
    [analysis appendFormat:@"Sandboxed: %s\n", constraints.isSandboxed ? "YES" : "NO"];
    [analysis appendFormat:@"Has Entitlements: %s\n", constraints.hasEntitlements ? "YES" : "NO"];
    
    [analysis appendString:@"\nAvailable APIs:\n"];
    for (const auto& api : constraints.availableAPIs) {
        [analysis appendFormat:@"• %s\n", api.c_str()];
    }
    
    if (self.processDetailsView) {
        self.processDetailsView.string = analysis;
    }
    
    [self updateStatus:@"Process analysis complete"];
}

#pragma mark - Utility Methods

- (void)appendToOutput:(NSString *)text {
    if (self.outputConsole) {
        NSString *currentText = self.outputConsole.string;
        NSString *newText = [currentText stringByAppendingString:text];
        self.outputConsole.string = newText;
        
        // Scroll to bottom
        NSRange range = NSMakeRange(newText.length, 0);
        [self.outputConsole scrollRangeToVisible:range];
    }
}

- (void)updateStatus:(NSString *)status {
    if (self.statusLabel) {
        self.statusLabel.stringValue = status;
    }
    NSLog(@"Status: %@", status);
}

- (void)showSecurityWarning:(NSString *)warning {
    [SecurityAlertController showSecurityWarning:warning inWindow:self.window];
}

- (void)validateSecurityCompliance {
    if (!_processAnalyzer || !_luaEngine || !_memoryAnalyzer) {
        [SecurityAlertController showSecurityWarning:@"Research framework not properly initialized" 
                                            inWindow:self.window];
        return;
    }
    
    BOOL compliant = _processAnalyzer->validateSecurityCompliance() &&
                    _luaEngine->validateSandboxSecurity() &&
                    _memoryAnalyzer->validateSecurityCompliance();
    
    [SecurityAlertController showSecurityComplianceStatus:compliant inWindow:self.window];
}

@end

#pragma mark - ProcessTableDataSource Implementation

@implementation ProcessTableDataSource

- (instancetype)initWithMainWindow:(ResearchMainWindow *)mainWindow {
    self = [super init];
    if (self) {
        _mainWindow = mainWindow;
        _processes = [[NSMutableArray alloc] init];
    }
    return self;
}

- (NSInteger)numberOfRowsInTableView:(NSTableView *)tableView {
    return self.processes.count;
}

- (id)tableView:(NSTableView *)tableView objectValueForTableColumn:(NSTableColumn *)tableColumn row:(NSInteger)row {
    if (row < 0 || row >= self.processes.count) return nil;
    
    NSDictionary *process = self.processes[row];
    NSString *identifier = tableColumn.identifier;
    
    if ([identifier isEqualToString:@"pid"]) {
        return process[@"pid"];
    } else if ([identifier isEqualToString:@"name"]) {
        return process[@"name"];
    } else if ([identifier isEqualToString:@"isRoblox"]) {
        return [process[@"isRoblox"] boolValue] ? @"Yes" : @"No";
    } else if ([identifier isEqualToString:@"architecture"]) {
        return process[@"architecture"];
    }
    
    return nil;
}

@end

#pragma mark - SecurityAlertController Implementation

@implementation SecurityAlertController

+ (void)showSecurityWarning:(NSString *)message inWindow:(NSWindow *)window {
    NSAlert *alert = [[NSAlert alloc] init];
    alert.messageText = @"Security Warning";
    alert.informativeText = message;
    alert.alertStyle = NSAlertStyleWarning;
    [alert addButtonWithTitle:@"OK"];
    
    if (window) {
        [alert beginSheetModalForWindow:window completionHandler:nil];
    } else {
        [alert runModal];
    }
}

+ (void)showSIPDisabledWarning:(NSWindow *)window {
    [self showSecurityWarning:@"System Integrity Protection (SIP) is disabled. This compromises the validity of security research. Please enable SIP for legitimate research." 
                     inWindow:window];
}

+ (void)showSecurityComplianceStatus:(BOOL)compliant inWindow:(NSWindow *)window {
    NSAlert *alert = [[NSAlert alloc] init];
    
    if (compliant) {
        alert.messageText = @"Security Compliance Validated";
        alert.informativeText = @"All research framework components are operating within security constraints. Research results will be valid.";
        alert.alertStyle = NSAlertStyleInformational;
    } else {
        alert.messageText = @"Security Compliance Failed";
        alert.informativeText = @"One or more framework components are not operating within security constraints. Research validity may be compromised.";
        alert.alertStyle = NSAlertStyleCritical;
    }
    
    [alert addButtonWithTitle:@"OK"];
    
    if (window) {
        [alert beginSheetModalForWindow:window completionHandler:nil];
    } else {
        [alert runModal];
    }
}

#pragma mark - Enhanced UI Functionality

- (void)setupRealTimeMonitoring {
    // Initialize real-time monitoring timer
    _monitoringTimer = [NSTimer scheduledTimerWithTimeInterval:2.0
                                                        target:self
                                                      selector:@selector(updateRealTimeData:)
                                                      userInfo:nil
                                                       repeats:YES];

    // Setup security event monitoring
    [self initializeSecurityEventMonitoring];

    NSLog(@"Real-time monitoring initialized");
}

- (void)initializeSecurityFramework {
    // Initialize advanced security analysis components
    try {
        if (!_codeSigningAnalyzer) {
            _codeSigningAnalyzer = new RobloxResearch::SecurityAnalysis::CodeSigningAnalyzer();
        }

        // Setup security compliance validation
        [self performSecurityComplianceCheck];

        NSLog(@"Enhanced security framework initialized");
    } catch (const std::exception& e) {
        NSLog(@"Error initializing security framework: %s", e.what());
        [self showSecurityWarning:[NSString stringWithFormat:@"Security framework error: %s", e.what()]];
    }
}

- (void)initializeSecurityEventMonitoring {
    // Setup distributed notification center for security events
    [[NSDistributedNotificationCenter defaultCenter]
        addObserver:self
           selector:@selector(handleSecurityEvent:)
               name:@"RobloxResearchSecurityEvent"
             object:nil];

    // Initialize security event log
    _securityEventLog = [[NSMutableArray alloc] init];
}

- (void)updateRealTimeData:(NSTimer *)timer {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // Update process monitoring data
        [self refreshProcessMonitoringData];

        // Update security status
        [self updateSecurityStatus];

        // Update memory analysis
        [self updateMemoryAnalysis];

        dispatch_async(dispatch_get_main_queue(), ^{
            // Update UI elements
            [self.processTable reloadData];
            [self updateSecurityIndicators];
        });
    });
}

- (void)refreshProcessMonitoringData {
    if (!_processAnalyzer) return;

    try {
        // Get current process list with security analysis
        auto processes = _processAnalyzer->getRunningProcesses();

        [_processData removeAllObjects];

        for (const auto& process : processes) {
            NSMutableDictionary *processInfo = [[NSMutableDictionary alloc] init];
            processInfo[@"pid"] = @(process.pid);
            processInfo[@"name"] = [NSString stringWithUTF8String:process.name.c_str()];
            processInfo[@"path"] = [NSString stringWithUTF8String:process.executablePath.c_str()];
            processInfo[@"signed"] = @(process.isCodeSigned);
            processInfo[@"sandboxed"] = @(process.isSandboxed);
            processInfo[@"sip_protected"] = @(process.isSIPProtected);

            [_processData addObject:processInfo];
        }
    } catch (const std::exception& e) {
        NSLog(@"Error refreshing process data: %s", e.what());
    }
}

- (void)updateSecurityStatus {
    if (!_codeSigningAnalyzer) return;

    try {
        // Validate system security posture
        auto securityValidation = _codeSigningAnalyzer->validateSystemSecurity();

        dispatch_async(dispatch_get_main_queue(), ^{
            NSMutableString *statusText = [[NSMutableString alloc] init];
            [statusText appendString:@"=== Real-Time Security Status ===\n"];

            for (const auto& status : securityValidation) {
                [statusText appendFormat:@"%s\n", status.c_str()];
            }

            if (self.securityAnalysis) {
                self.securityAnalysis.string = statusText;
            }
        });
    } catch (const std::exception& e) {
        NSLog(@"Error updating security status: %s", e.what());
    }
}

- (void)updateMemoryAnalysis {
    if (!_memoryAnalyzer) return;

    try {
        // Run memory protection analysis
        auto memoryReport = RobloxResearch::SecurityAnalysis::MemoryProtectionAnalyzer::generateMemorySecurityReport();

        // Store for display in security analysis view
        _lastMemoryReport = [NSString stringWithUTF8String:memoryReport.c_str()];
    } catch (const std::exception& e) {
        NSLog(@"Error updating memory analysis: %s", e.what());
    }
}

- (void)updateSecurityIndicators {
    // Update security compliance indicators in the UI
    if (_sipEnabled) {
        [self.statusLabel setTextColor:[NSColor greenColor]];
        [self updateStatus:@"✓ Security Compliance: ACTIVE - SIP Enabled"];
    } else {
        [self.statusLabel setTextColor:[NSColor redColor]];
        [self updateStatus:@"✗ Security Compliance: COMPROMISED - SIP Disabled"];
    }
}

- (void)performSecurityComplianceCheck {
    // Check SIP status
    int sipStatus = 0;
    size_t size = sizeof(sipStatus);
    _sipEnabled = (sysctlbyname("kern.sip_status", &sipStatus, &size, nullptr, 0) == 0) && (sipStatus != 0);

    // Log security compliance status
    if (_sipEnabled) {
        NSLog(@"Security Compliance: SIP is enabled - Research environment is valid");
    } else {
        NSLog(@"Security Warning: SIP is disabled - Research validity may be compromised");
        [self showSecurityWarning:@"System Integrity Protection is disabled. Research results may not be representative of a secure macOS environment."];
    }
}

- (void)handleSecurityEvent:(NSNotification *)notification {
    NSDictionary *eventInfo = notification.userInfo;

    // Log security event
    NSMutableDictionary *logEntry = [[NSMutableDictionary alloc] init];
    logEntry[@"timestamp"] = [NSDate date];
    logEntry[@"event"] = eventInfo[@"event"] ?: @"Unknown";
    logEntry[@"details"] = eventInfo[@"details"] ?: @"No details";

    [_securityEventLog addObject:logEntry];

    // Update UI with security event
    dispatch_async(dispatch_get_main_queue(), ^{
        [self displaySecurityEvent:logEntry];
    });
}

- (void)displaySecurityEvent:(NSDictionary *)event {
    NSString *eventText = [NSString stringWithFormat:@"[%@] %@: %@\n",
                          event[@"timestamp"],
                          event[@"event"],
                          event[@"details"]];

    if (self.outputConsole) {
        NSMutableString *currentText = [[NSMutableString alloc] initWithString:self.outputConsole.string];
        [currentText appendString:eventText];
        self.outputConsole.string = currentText;

        // Scroll to bottom
        [self.outputConsole scrollRangeToVisible:NSMakeRange(currentText.length, 0)];
    }
}

#pragma mark - Advanced Analysis Actions

- (IBAction)runComprehensiveSecurityTest:(id)sender {
    [self updateStatus:@"Running comprehensive security test suite..."];
    [self.progressIndicator startAnimation:nil];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            // Run Lua security tests
            auto luaReport = _luaEngine->runSecurityTestSuite();

            // Run memory protection tests
            auto memoryReport = RobloxResearch::SecurityAnalysis::MemoryProtectionAnalyzer::testMemoryProtectionMechanisms();

            // Run code signing analysis
            auto codeSigningReport = _codeSigningAnalyzer->generateSecurityResearchReport();

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayComprehensiveTestResults:luaReport memoryReport:memoryReport codeSigningReport:codeSigningReport];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Comprehensive security test completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.progressIndicator stopAnimation:nil];
                [self showSecurityWarning:[NSString stringWithFormat:@"Security test error: %s", e.what()]];
            });
        }
    });
}

- (void)displayComprehensiveTestResults:(const RobloxResearch::LuaRuntime::SandboxedLuaEngine::ComprehensiveSecurityReport&)luaReport
                           memoryReport:(const RobloxResearch::SecurityAnalysis::MemoryProtectionAnalyzer::AdvancedMemoryAnalysis&)memoryReport
                      codeSigningReport:(const std::string&)codeSigningReport {

    NSMutableString *results = [[NSMutableString alloc] init];

    [results appendString:@"=== COMPREHENSIVE SECURITY TEST RESULTS ===\n\n"];

    // Lua Security Test Results
    [results appendString:@"=== LUA RUNTIME SECURITY ===\n"];
    [results appendFormat:@"Overall Security: %s\n", luaReport.overallSecurityValid ? "VALID" : "COMPROMISED"];
    [results appendFormat:@"Security Level: %s\n", luaReport.securityLevel.c_str()];
    [results appendFormat:@"Tests Completed: %lu\n\n", luaReport.testResults.size()];

    for (const auto& test : luaReport.testResults) {
        [results appendFormat:@"Test: %s - %s\n", test.testName.c_str(), test.passed ? "PASS" : "FAIL"];
        if (!test.violations.empty()) {
            for (const auto& violation : test.violations) {
                [results appendFormat:@"  - %s\n", violation.c_str()];
            }
        }
    }

    [results appendString:@"\n=== MEMORY PROTECTION ANALYSIS ===\n"];
    [results appendFormat:@"Assessment: %s\n\n", memoryReport.overallAssessment.c_str()];

    for (const auto& test : memoryReport.protectionTests) {
        [results appendFormat:@"Test: %s - %s\n", test.testName.c_str(), test.testPassed ? "PASS" : "FAIL"];
        [results appendFormat:@"Description: %s\n", test.description.c_str()];
        [results appendFormat:@"Security Implication: %s\n\n", test.securityImplication.c_str()];
    }

    [results appendString:@"\n=== CODE SIGNING ANALYSIS ===\n"];
    [results appendFormat:@"%s\n", codeSigningReport.c_str()];

    // Display results
    if (self.securityAnalysis) {
        self.securityAnalysis.string = results;
    }
}

- (IBAction)analyzeRobloxDownloaderScript:(id)sender {
    [self updateStatus:@"Analyzing Roblox downloader script security implications..."];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            auto analysis = _codeSigningAnalyzer->analyzeRobloxDownloaderScript();
            auto effects = _codeSigningAnalyzer->simulateDownloaderEffects();

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayDownloaderAnalysis:analysis effects:effects];
                [self updateStatus:@"Downloader script analysis completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Analysis error: %s", e.what()]];
            });
        }
    });
}

- (void)displayDownloaderAnalysis:(const RobloxResearch::SecurityAnalysis::CodeSigningAnalyzer::DownloaderScriptAnalysis&)analysis
                          effects:(const std::vector<std::string>&)effects {

    NSMutableString *results = [[NSMutableString alloc] init];

    [results appendString:@"=== ROBLOX DOWNLOADER SCRIPT ANALYSIS ===\n\n"];

    [results appendString:@"Security Actions:\n"];
    for (const auto& action : analysis.securityActions) {
        [results appendFormat:@"- %s\n", action.c_str()];
    }

    [results appendString:@"\nBypassed Mechanisms:\n"];
    for (const auto& mechanism : analysis.bypassedMechanisms) {
        [results appendFormat:@"- %s\n", mechanism.c_str()];
    }

    [results appendString:@"\nRemaining Protections:\n"];
    for (const auto& protection : analysis.remainingProtections) {
        [results appendFormat:@"- %s\n", protection.c_str()];
    }

    [results appendString:@"\nSecurity Implications:\n"];
    for (const auto& implication : analysis.securityImplications) {
        [results appendFormat:@"- %s\n", implication.c_str()];
    }

    [results appendFormat:@"\nRisk Assessment:\n%s\n\n", analysis.overallRiskAssessment.c_str()];

    [results appendString:@"=== SCRIPT EFFECTS SIMULATION ===\n"];
    for (const auto& effect : effects) {
        [results appendFormat:@"%s\n", effect.c_str()];
    }

    if (self.securityAnalysis) {
        self.securityAnalysis.string = results;
    }
}

// Enhanced Action Methods
- (IBAction)downloadAndAnalyzeRoblox:(id)sender {
    [self updateStatus:@"Downloading and analyzing Roblox installer..."];
    [self.progressIndicator startAnimation:nil];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            std::string downloadPath = "/tmp/RobloxInstaller.dmg";
            auto report = self->_robloxAnalyzer->analyzeRobloxInstaller(downloadPath);

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayRobloxAnalysis:report];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Roblox analysis completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Analysis error: %s", e.what()]];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Analysis failed"];
            });
        }
    });
}

- (IBAction)analyzeInstalledRoblox:(id)sender {
    [self updateStatus:@"Analyzing installed Roblox application..."];
    [self.progressIndicator startAnimation:nil];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            auto report = self->_robloxAnalyzer->analyzeInstalledApplication();

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayRobloxAnalysis:report];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Installed Roblox analysis completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Analysis error: %s", e.what()]];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Analysis failed"];
            });
        }
    });
}

- (IBAction)createEducationalDylib:(id)sender {
    [self updateStatus:@"Creating educational dynamic library..."];

    NSSavePanel *savePanel = [NSSavePanel savePanel];
    savePanel.title = @"Save Educational Dylib";
    savePanel.nameFieldStringValue = @"educational_dylib";
    savePanel.allowedFileTypes = @[@"dylib"];

    [savePanel beginSheetModalForWindow:self.window completionHandler:^(NSInteger result) {
        if (result == NSModalResponseOK) {
            NSURL *url = [savePanel URL];
            std::string outputPath = [[url path] UTF8String];

            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                try {
                    bool success = self->_dylibFramework->createEducationalDylib(outputPath, "basic");

                    dispatch_async(dispatch_get_main_queue(), ^{
                        if (success) {
                            [self appendToOutput:@"[EDUCATIONAL] Educational dylib created successfully\n"];
                            [self appendToOutput:@"[EDUCATIONAL] This demonstrates dylib structure and security constraints\n"];
                            [self updateStatus:@"Educational dylib created"];
                        } else {
                            [self showSecurityWarning:@"Failed to create educational dylib"];
                            [self updateStatus:@"Dylib creation failed"];
                        }
                    });
                } catch (const std::exception& e) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        [self showSecurityWarning:[NSString stringWithFormat:@"Dylib creation error: %s", e.what()]];
                        [self updateStatus:@"Dylib creation failed"];
                    });
                }
            });
        }
    }];
}

- (IBAction)demonstrateInjectionAttempt:(id)sender {
    [self updateStatus:@"Demonstrating injection attempt (educational)..."];
    [self appendToOutput:@"[EDUCATIONAL] Demonstrating why injection attempts fail on modern macOS\n"];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            using InjectionMethod = RobloxResearch::ProcessInteraction::EducationalDylibFramework::InjectionMethod;

            auto attempt = self->_dylibFramework->demonstrateInjectionAttempt(
                InjectionMethod::DYLD_INSERT_LIBRARIES,
                "RobloxPlayer",
                "/tmp/educational.dylib"
            );

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayInjectionAttemptResults:attempt];
                [self updateStatus:@"Injection demonstration completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Demonstration error: %s", e.what()]];
                [self updateStatus:@"Demonstration failed"];
            });
        }
    });
}

- (IBAction)testSecurityConstraints:(id)sender {
    [self updateStatus:@"Testing security constraints..."];
    [self appendToOutput:@"[EDUCATIONAL] Testing macOS security constraints\n"];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            using SecurityConstraint = RobloxResearch::ProcessInteraction::EducationalDylibFramework::SecurityConstraint;

            std::vector<SecurityConstraint> constraints = {
                SecurityConstraint::SIP_PROTECTION,
                SecurityConstraint::CODE_SIGNING,
                SecurityConstraint::LIBRARY_VALIDATION,
                SecurityConstraint::SANDBOX_RESTRICTIONS,
                SecurityConstraint::ENTITLEMENT_REQUIRED
            };

            NSMutableString *results = [[NSMutableString alloc] init];
            [results appendString:@"[EDUCATIONAL] Security Constraint Test Results:\n"];

            for (const auto& constraint : constraints) {
                std::string result = self->_dylibFramework->testSecurityConstraint(constraint);
                [results appendFormat:@"  - %s\n", result.c_str()];
            }

            dispatch_async(dispatch_get_main_queue(), ^{
                [self appendToOutput:results];
                [self updateStatus:@"Security constraint testing completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Security test error: %s", e.what()]];
                [self updateStatus:@"Security testing failed"];
            });
        }
    });
}

- (IBAction)loadScriptTemplate:(id)sender {
    if (self.scriptTemplatePopup.selectedItem) {
        NSString *templateName = self.scriptTemplatePopup.selectedItem.title;
        ScriptTemplateManager *manager = [ScriptTemplateManager sharedManager];
        NSString *templateContent = [manager getTemplate:templateName];

        if (templateContent) {
            self.scriptEditor.string = templateContent;
            [self appendToOutput:[NSString stringWithFormat:@"[EDUCATIONAL] Loaded template: %@\n", templateName]];
            [self updateStatus:[NSString stringWithFormat:@"Template '%@' loaded", templateName]];
        }
    }
}

- (IBAction)runAdvancedSecuritySuite:(id)sender {
    [self updateStatus:@"Running advanced security test suite..."];
    [self.progressIndicator startAnimation:nil];

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        try {
            auto report = self->_luaEngine->runSecurityTestSuite();

            dispatch_async(dispatch_get_main_queue(), ^{
                [self displayAdvancedSecurityReport:report];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Advanced security suite completed"];
            });
        } catch (const std::exception& e) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self showSecurityWarning:[NSString stringWithFormat:@"Security suite error: %s", e.what()]];
                [self.progressIndicator stopAnimation:nil];
                [self updateStatus:@"Security suite failed"];
            });
        }
    });
}

- (void)dealloc {
    [_monitoringTimer invalidate];
    [[NSDistributedNotificationCenter defaultCenter] removeObserver:self];

    delete _processAnalyzer;
    delete _luaEngine;
    delete _memoryAnalyzer;
    delete _codeSigningAnalyzer;
    delete _robloxAnalyzer;
    delete _dylibFramework;
}

// Enhanced Display Methods
- (void)displayRobloxAnalysis:(const RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer::ComprehensiveAnalysisReport&)report {
    NSMutableString *analysis = [[NSMutableString alloc] init];

    [analysis appendString:@"=== ROBLOX APPLICATION SECURITY ANALYSIS ===\n\n"];

    // Application Info
    [analysis appendString:@"Application Information:\n"];
    [analysis appendFormat:@"  Bundle ID: %s\n", report.appInfo.bundleIdentifier.c_str()];
    [analysis appendFormat:@"  Version: %s\n", report.appInfo.version.c_str()];
    [analysis appendFormat:@"  Installed: %s\n", report.appInfo.isInstalled ? "Yes" : "No"];
    [analysis appendFormat:@"  Architecture: %s\n", report.appInfo.architectures.c_str()];
    [analysis appendString:@"\n"];

    // Code Signing Info
    [analysis appendString:@"Code Signing Analysis:\n"];
    [analysis appendFormat:@"  Signed: %s\n", report.codeSigningInfo.isSigned ? "Yes" : "No"];
    [analysis appendFormat:@"  Valid Signature: %s\n", report.codeSigningInfo.isValidSignature ? "Yes" : "No"];
    [analysis appendFormat:@"  Signing Identity: %s\n", report.codeSigningInfo.signingIdentity.c_str()];
    [analysis appendFormat:@"  Hardened Runtime: %s\n", report.codeSigningInfo.hasHardenedRuntime ? "Yes" : "No"];
    [analysis appendString:@"\n"];

    // Security Features
    [analysis appendString:@"Security Features:\n"];
    [analysis appendFormat:@"  SIP Protected: %s\n", report.securityFeatures.sipProtected ? "Yes" : "No"];
    [analysis appendFormat:@"  Sandboxed: %s\n", report.securityFeatures.sandboxed ? "Yes" : "No"];
    [analysis appendString:@"\n"];

    // Educational Findings
    [analysis appendString:@"Educational Research Findings:\n"];
    for (const auto& finding : report.researchFindings) {
        [analysis appendFormat:@"  - %s\n", finding.c_str()];
    }
    [analysis appendString:@"\n"];

    // Security Recommendations
    [analysis appendString:@"Security Recommendations:\n"];
    for (const auto& recommendation : report.securityRecommendations) {
        [analysis appendFormat:@"  - %s\n", recommendation.c_str()];
    }

    if (self.robloxAnalysisView) {
        self.robloxAnalysisView.string = analysis;
    } else {
        [self appendToOutput:analysis];
    }
}

- (void)displayInjectionAttemptResults:(const RobloxResearch::ProcessInteraction::EducationalDylibFramework::InjectionAttempt&)attempt {
    NSMutableString *results = [[NSMutableString alloc] init];

    [results appendString:@"[EDUCATIONAL] Injection Attempt Analysis:\n"];
    [results appendFormat:@"  Target Process: %s\n", attempt.targetProcess.c_str()];
    [results appendFormat:@"  Method: %d\n", static_cast<int>(attempt.method)];
    [results appendFormat:@"  Success: %s (Expected: No)\n", attempt.successful ? "Yes" : "No"];
    [results appendFormat:@"  Error: %s\n", attempt.errorMessage.c_str()];
    [results appendString:@"\n  Security Constraints that Blocked Injection:\n"];

    for (const auto& constraint : attempt.blockedBy) {
        [results appendFormat:@"    - %d\n", static_cast<int>(constraint)];
    }

    [results appendString:@"\n  Educational Notes:\n"];
    [results appendFormat:@"    %s\n", attempt.educationalNotes.c_str()];
    [results appendString:@"\n"];

    [self appendToOutput:results];
}

- (void)displayAdvancedSecurityReport:(const RobloxResearch::LuaRuntime::SandboxedLuaEngine::ComprehensiveSecurityReport&)report {
    NSMutableString *reportText = [[NSMutableString alloc] init];

    [reportText appendString:@"=== ADVANCED SECURITY TEST SUITE RESULTS ===\n\n"];
    [reportText appendFormat:@"Overall Security Status: %s\n", report.overallSecurityValid ? "SECURE" : "ISSUES DETECTED"];
    [reportText appendFormat:@"Security Level: %s\n", report.securityLevel.c_str()];
    [reportText appendString:@"\n"];

    [reportText appendString:@"Test Results:\n"];
    for (const auto& test : report.testResults) {
        [reportText appendFormat:@"  %s: %s\n", test.testName.c_str(), test.passed ? "PASS" : "FAIL"];
        [reportText appendFormat:@"    Description: %s\n", test.description.c_str()];
        if (!test.violations.empty()) {
            [reportText appendString:@"    Violations:\n"];
            for (const auto& violation : test.violations) {
                [reportText appendFormat:@"      - %s\n", violation.c_str()];
            }
        }
        [reportText appendString:@"\n"];
    }

    [reportText appendString:@"Security Recommendations:\n"];
    for (const auto& recommendation : report.recommendations) {
        [reportText appendFormat:@"  - %s\n", recommendation.c_str()];
    }

    [self appendToOutput:reportText];
}

- (void)displayRobloxAnalysisReport:(const RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer::AnalysisReport&)report {
    NSMutableString *output = [[NSMutableString alloc] init];

    [output appendString:@"=== ROBLOX APPLICATION ANALYSIS ===\n\n"];

    [output appendFormat:@"Application Found: %s\n", report.applicationFound ? "Yes" : "No"];
    [output appendFormat:@"Installation Path: %s\n", report.installationPath.c_str()];
    [output appendFormat:@"Version: %s\n", report.version.c_str()];
    [output appendFormat:@"Bundle Identifier: %s\n", report.bundleIdentifier.c_str()];

    [output appendString:@"\nSecurity Features:\n"];
    for (const auto& feature : report.securityFeatures) {
        [output appendFormat:@"  - %s\n", feature.c_str()];
    }

    [output appendString:@"\nResearch Findings:\n"];
    for (const auto& finding : report.researchFindings) {
        [output appendFormat:@"  - %s\n", finding.c_str()];
    }

    [output appendString:@"\nSecurity Recommendations:\n"];
    for (const auto& recommendation : report.securityRecommendations) {
        [output appendFormat:@"  - %s\n", recommendation.c_str()];
    }

    [self appendToOutput:output];
}

- (void)displayIntegrationDemo:(const std::string&)demo {
    NSString *output = [NSString stringWithUTF8String:demo.c_str()];
    [self appendToOutput:output];
}

- (void)displayTestSuiteReport:(const RobloxResearch::Testing::ComprehensiveTestSuite::TestSuiteReport&)report
                complianceReport:(const std::string&)complianceReport {
    NSMutableString *output = [[NSMutableString alloc] init];

    [output appendString:@"=== COMPREHENSIVE TEST SUITE RESULTS ===\n\n"];

    [output appendFormat:@"Total Tests: %lu\n", report.testReports.size()];
    [output appendFormat:@"Success Rate: %.1f%%\n", report.successRate];
    [output appendFormat:@"Execution Time: %lldms\n", report.totalExecutionTime.count()];
    [output appendFormat:@"Overall Success: %s\n\n", report.overallSuccess ? "Yes" : "No"];

    if (!report.criticalFailures.empty()) {
        [output appendString:@"Critical Failures:\n"];
        for (const auto& failure : report.criticalFailures) {
            [output appendFormat:@"  - %s\n", failure.c_str()];
        }
        [output appendString:@"\n"];
    }

    if (!report.securityWarnings.empty()) {
        [output appendString:@"Security Warnings:\n"];
        for (const auto& warning : report.securityWarnings) {
            [output appendFormat:@"  - %s\n", warning.c_str()];
        }
        [output appendString:@"\n"];
    }

    [output appendString:@"Educational Notes:\n"];
    for (const auto& note : report.educationalNotes) {
        [output appendFormat:@"  - %s\n", note.c_str()];
    }

    [output appendString:@"\n"];
    [output appendString:[NSString stringWithUTF8String:complianceReport.c_str()]];

    [self appendToOutput:output];
}

@end
