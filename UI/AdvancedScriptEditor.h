#pragma once

#import <Cocoa/Cocoa.h>
#include "../Core/LuaRuntime/SandboxedLuaEngine.h"

@interface AdvancedScriptEditor : NSTextView <NSTextViewDelegate>

// Syntax highlighting
@property (nonatomic, strong) NSMutableDictionary *syntaxColors;
@property (nonatomic, strong) NSTimer *highlightingTimer;
@property (nonatomic) BOOL syntaxHighlightingEnabled;

// Code completion
@property (nonatomic, strong) NSArray *luaKeywords;
@property (nonatomic, strong) NSArray *robloxAPI;
@property (nonatomic, strong) NSPopover *completionPopover;
@property (nonatomic, strong) NSTableView *completionTableView;

// Error highlighting
@property (nonatomic, strong) NSMutableArray *errorRanges;
@property (nonatomic, strong) NSColor *errorHighlightColor;

// Line numbers
@property (nonatomic, strong) NSScrollView *lineNumberScrollView;
@property (nonatomic, strong) NSTextView *lineNumberView;
@property (nonatomic) BOOL showLineNumbers;

// Security validation
@property (nonatomic) RobloxResearch::LuaRuntime::SandboxedLuaEngine *luaEngine;
@property (nonatomic, strong) NSMutableArray *securityWarnings;

// Real-time validation
@property (nonatomic, strong) NSTimer *validationTimer;
@property (nonatomic) BOOL realTimeValidationEnabled;

// Script templates
@property (nonatomic, strong) NSArray *scriptTemplates;

// Initialization
- (instancetype)initWithFrame:(NSRect)frameRect;
- (void)setupAdvancedFeatures;

// Syntax Highlighting
- (void)enableSyntaxHighlighting:(BOOL)enabled;
- (void)highlightSyntax;
- (void)highlightLuaKeywords;
- (void)highlightStrings;
- (void)highlightComments;
- (void)highlightNumbers;
- (void)scheduleHighlighting;

// Code Completion
- (void)setupCodeCompletion;
- (void)showCompletionPopover:(NSRange)range;
- (void)hideCompletionPopover;
- (NSArray *)getCompletionsForPrefix:(NSString *)prefix;
- (void)insertCompletion:(NSString *)completion atRange:(NSRange)range;

// Error Highlighting
- (void)highlightErrors:(NSArray *)errors;
- (void)clearErrorHighlights;
- (void)addErrorHighlight:(NSRange)range withMessage:(NSString *)message;

// Line Numbers
- (void)enableLineNumbers:(BOOL)enabled;
- (void)updateLineNumbers;
- (void)setupLineNumberView;

// Security Validation
- (void)enableRealTimeValidation:(BOOL)enabled;
- (void)validateScriptSecurity;
- (void)displaySecurityWarnings:(NSArray *)warnings;
- (void)clearSecurityWarnings;

// Script Templates
- (void)loadScriptTemplates;
- (void)insertTemplate:(NSString *)templateName;
- (NSArray *)getAvailableTemplates;

// Utility Methods
- (NSString *)getCurrentWord;
- (NSRange)getCurrentWordRange;
- (NSString *)getTextInRange:(NSRange)range;
- (void)insertTextAtCursor:(NSString *)text;

// Enhanced Features
- (void)setupAdvancedTextFeatures;
- (void)enableBracketMatching:(BOOL)enabled;
- (void)enableAutoIndentation:(BOOL)enabled;
- (void)enableSmartQuotes:(BOOL)enabled;

@end

// Code Completion Data Source
@interface CodeCompletionDataSource : NSObject <NSTableViewDataSource, NSTableViewDelegate>

@property (nonatomic, strong) NSArray *completions;
@property (nonatomic, weak) AdvancedScriptEditor *editor;

- (instancetype)initWithEditor:(AdvancedScriptEditor *)editor;
- (void)updateCompletions:(NSArray *)completions;

@end

// Line Number View
@interface LineNumberView : NSTextView

@property (nonatomic, weak) AdvancedScriptEditor *mainEditor;
@property (nonatomic, strong) NSColor *lineNumberColor;
@property (nonatomic, strong) NSFont *lineNumberFont;

- (instancetype)initWithMainEditor:(AdvancedScriptEditor *)editor;
- (void)updateLineNumbers;

@end

// Security Warning View
@interface SecurityWarningView : NSView

@property (nonatomic, strong) NSMutableArray *warnings;
@property (nonatomic, strong) NSColor *warningColor;
@property (nonatomic, strong) NSFont *warningFont;

- (void)addWarning:(NSString *)warning atLine:(NSInteger)line;
- (void)clearWarnings;
- (void)displayWarnings;

@end

// Script Template Manager
@interface ScriptTemplateManager : NSObject

@property (nonatomic, strong) NSMutableDictionary *templates;

+ (instancetype)sharedManager;
- (void)loadDefaultTemplates;
- (NSString *)getTemplate:(NSString *)name;
- (NSArray *)getTemplateNames;
- (void)addTemplate:(NSString *)name content:(NSString *)content;

@end

// Syntax Highlighter
@interface LuaSyntaxHighlighter : NSObject

@property (nonatomic, strong) NSDictionary *syntaxRules;
@property (nonatomic, strong) NSDictionary *colorScheme;

+ (instancetype)sharedHighlighter;
- (void)highlightText:(NSMutableAttributedString *)text inRange:(NSRange)range;
- (void)setupDefaultColorScheme;
- (void)setupSyntaxRules;

@end

// Real-time Validator
@interface RealTimeValidator : NSObject

@property (nonatomic) RobloxResearch::LuaRuntime::SandboxedLuaEngine *luaEngine;
@property (nonatomic, weak) AdvancedScriptEditor *editor;

- (instancetype)initWithEditor:(AdvancedScriptEditor *)editor;
- (void)validateScript:(NSString *)script;
- (NSArray *)getSecurityWarnings:(NSString *)script;
- (NSArray *)getSyntaxErrors:(NSString *)script;

@end
