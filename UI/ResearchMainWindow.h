#pragma once

#import <Cocoa/Cocoa.h>
#include "../Core/ProcessMonitor/ProcessAnalyzer.h"
#include "../Core/LuaRuntime/SandboxedLuaEngine.h"
#include "../Core/LuaRuntime/RobloxAPISimulator.h"
#include "../Core/SecurityAnalysis/MemoryAnalyzer.h"
#include "../Core/SecurityAnalysis/CodeSigningAnalyzer.h"
#include "../Core/SecurityAnalysis/RobloxApplicationAnalyzer.h"
#include "../Core/ProcessInteraction/EducationalDylibFramework.h"
#include "../Core/Integration/SecureIntegrationFramework.h"
#include "../Core/SecurityAnalysis/RobloxApplicationAnalyzer.h"
#include "../Tests/ComprehensiveTestSuite.h"
#include "../Core/ProcessInteraction/EducationalDylibFramework.h"
#import "AdvancedScriptEditor.h"

@interface ResearchMainWindow : NSWindowController <NSWindowDelegate, NSTextViewDelegate>

// Enhanced UI Components
@property (weak) IBOutlet AdvancedScriptEditor *scriptEditor;
@property (weak) IBOutlet NSTextView *outputConsole;
@property (weak) IBOutlet NSTableView *processTable;
@property (weak) IBOutlet NSTextView *securityAnalysis;
@property (weak) IBOutlet NSButton *executeButton;
@property (weak) IBOutlet NSButton *analyzeProcessButton;
@property (weak) IBOutlet NSButton *memoryAnalysisButton;
@property (weak) IBOutlet NSProgressIndicator *progressIndicator;
@property (weak) IBOutlet NSTextField *statusLabel;

// New Enhanced Components
@property (weak) IBOutlet NSButton *downloadRobloxButton;
@property (weak) IBOutlet NSButton *analyzeRobloxButton;
@property (weak) IBOutlet NSButton *createDylibButton;
@property (weak) IBOutlet NSButton *testSecurityButton;
@property (weak) IBOutlet NSTextView *robloxAnalysisView;
@property (weak) IBOutlet NSTextView *dylibAnalysisView;
@property (weak) IBOutlet NSPopUpButton *scriptTemplatePopup;
@property (weak) IBOutlet NSButton *loadTemplateButton;

// Tab View for different research areas
@property (weak) IBOutlet NSTabView *researchTabView;

// Process Analysis Tab
@property (weak) IBOutlet NSTextField *selectedProcessLabel;
@property (weak) IBOutlet NSTextView *processDetailsView;

// Memory Analysis Tab
@property (weak) IBOutlet NSTextView *memoryAnalysisView;
@property (weak) IBOutlet NSButton *memorySnapshotButton;

// Security Research Tab
@property (weak) IBOutlet NSTextView *securityResearchView;
@property (weak) IBOutlet NSButton *sipAnalysisButton;

// Enhanced Research Framework Components
@property (nonatomic, strong) NSMutableArray *processData;
@property (nonatomic) RobloxResearch::ProcessMonitor::ProcessAnalyzer *processAnalyzer;
@property (nonatomic) RobloxResearch::LuaRuntime::SandboxedLuaEngine *luaEngine;
@property (nonatomic) RobloxResearch::SecurityAnalysis::MemoryAnalyzer *memoryAnalyzer;
@property (nonatomic) RobloxResearch::SecurityAnalysis::CodeSigningAnalyzer *codeSigningAnalyzer;
@property (nonatomic) RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer *robloxAnalyzer;
@property (nonatomic) RobloxResearch::ProcessInteraction::EducationalDylibFramework *dylibFramework;

// Real-time Monitoring Components
@property (nonatomic, strong) NSTimer *monitoringTimer;
@property (nonatomic, strong) NSMutableArray *securityEventLog;
@property (nonatomic) BOOL sipEnabled;
@property (nonatomic, strong) NSString *lastMemoryReport;

// Window Management
- (instancetype)initWithWindowNibName:(NSString *)windowNibName;
- (void)windowDidLoad;
- (void)setupUI;
- (void)setupResearchFramework;

// Script Execution
- (IBAction)executeScript:(id)sender;
- (IBAction)clearOutput:(id)sender;
- (IBAction)loadSampleScript:(id)sender;

// Process Analysis
- (IBAction)refreshProcessList:(id)sender;
- (IBAction)analyzeSelectedProcess:(id)sender;
- (void)updateProcessTable;
- (void)displayProcessAnalysis:(const RobloxResearch::ProcessMonitor::ProcessAnalyzer::SecurityConstraints&)constraints 
                    forProcess:(pid_t)pid;

// Memory Analysis
- (IBAction)performMemoryAnalysis:(id)sender;
- (IBAction)takeMemorySnapshot:(id)sender;
- (void)displayMemoryAnalysis:(const RobloxResearch::SecurityAnalysis::MemoryAnalyzer::MemoryAnalysisReport&)report;

// Security Research
- (IBAction)analyzeSIPStatus:(id)sender;
- (IBAction)generateSecurityReport:(id)sender;
- (void)displaySecurityAnalysis:(const std::string&)analysis;

// Enhanced Security Analysis
- (IBAction)runComprehensiveSecurityTest:(id)sender;
- (IBAction)analyzeRobloxDownloaderScript:(id)sender;

// New Enhanced Features
- (IBAction)downloadAndAnalyzeRoblox:(id)sender;
- (IBAction)analyzeInstalledRoblox:(id)sender;
- (IBAction)createEducationalDylib:(id)sender;
- (IBAction)demonstrateInjectionAttempt:(id)sender;
- (IBAction)testSecurityConstraints:(id)sender;
- (IBAction)loadScriptTemplate:(id)sender;
- (IBAction)runAdvancedSecuritySuite:(id)sender;
- (void)displayRobloxAnalysis:(const RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer::ComprehensiveAnalysisReport&)report;
- (void)displayInjectionAttemptResults:(const RobloxResearch::ProcessInteraction::EducationalDylibFramework::InjectionAttempt&)attempt;
- (void)displayAdvancedSecurityReport:(const RobloxResearch::LuaRuntime::SandboxedLuaEngine::ComprehensiveSecurityReport&)report;

// Comprehensive Framework Features
- (IBAction)analyzeRobloxApplication:(id)sender;
- (IBAction)showIntegrationFramework:(id)sender;
- (IBAction)runComprehensiveTests:(id)sender;
- (void)displayRobloxAnalysisReport:(const RobloxResearch::SecurityAnalysis::RobloxApplicationAnalyzer::AnalysisReport&)report;
- (void)displayIntegrationDemo:(const std::string&)demo;
- (void)displayTestSuiteReport:(const RobloxResearch::Testing::ComprehensiveTestSuite::TestSuiteReport&)report
                complianceReport:(const std::string&)complianceReport;

// Real-time Monitoring
- (void)setupRealTimeMonitoring;
- (void)initializeSecurityFramework;
- (void)initializeSecurityEventMonitoring;
- (void)updateRealTimeData:(NSTimer *)timer;
- (void)refreshProcessMonitoringData;
- (void)updateSecurityStatus;
- (void)updateMemoryAnalysis;
- (void)updateSecurityIndicators;
- (void)performSecurityComplianceCheck;
- (void)handleSecurityEvent:(NSNotification *)notification;
- (void)displaySecurityEvent:(NSDictionary *)event;

// Utility Methods
- (void)appendToOutput:(NSString *)text;
- (void)updateStatus:(NSString *)status;
- (void)showSecurityWarning:(NSString *)warning;
- (void)validateSecurityCompliance;

@end

// Custom NSTableView Data Source for Process List
@interface ProcessTableDataSource : NSObject <NSTableViewDataSource, NSTableViewDelegate>

@property (nonatomic, strong) NSMutableArray *processes;
@property (nonatomic, weak) ResearchMainWindow *mainWindow;

- (instancetype)initWithMainWindow:(ResearchMainWindow *)mainWindow;
- (void)updateProcessData:(const std::vector<RobloxResearch::ProcessMonitor::ProcessAnalyzer::ProcessInfo>&)processes;

@end

// Custom NSTextView for Script Editor with Syntax Highlighting
@interface ScriptEditorTextView : NSTextView

- (void)setupSyntaxHighlighting;
- (void)highlightLuaSyntax;

@end

// Security Alert Controller
@interface SecurityAlertController : NSObject

+ (void)showSecurityWarning:(NSString *)message inWindow:(NSWindow *)window;
+ (void)showSIPDisabledWarning:(NSWindow *)window;
+ (void)showSecurityComplianceStatus:(BOOL)compliant inWindow:(NSWindow *)window;

@end
