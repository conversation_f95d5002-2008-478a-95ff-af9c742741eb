cmake_minimum_required(VERSION 3.20)
project(RobloxExecutorResearch)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# macOS specific settings
set(CMAKE_OSX_DEPLOYMENT_TARGET "11.0")
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm64")
    set(CMAKE_OSX_ARCHITECTURES "arm64")
else()
    set(CMAKE_OSX_ARCHITECTURES "x86_64")
endif()

# Find required packages
find_package(PkgConfig REQUIRED)

# LuaJIT configuration
find_path(LUAJIT_INCLUDE_DIR luajit.h
    HINTS
    /usr/local/include/luajit-2.1
    /opt/homebrew/include/luajit-2.1
    /usr/include/luajit-2.1
)

find_library(LUAJIT_LIBRARY
    NAMES luajit-5.1 luajit
    HINTS
    /usr/local/lib
    /opt/homebrew/lib
    /usr/lib
)

# Core library
add_library(RobloxResearchCore STATIC
    Core/ProcessMonitor/ProcessAnalyzer.cpp
    Core/ProcessMonitor/SecurityBoundaryChecker.cpp
    Core/LuaRuntime/SandboxedLuaEngine.cpp
    Core/LuaRuntime/SecurityWrapper.cpp
    Core/LuaRuntime/RobloxAPISimulator.cpp
    Core/SecurityAnalysis/MemoryAnalyzer.cpp
    Core/SecurityAnalysis/SIPCompliantTools.cpp
    Core/SecurityAnalysis/CodeSigningAnalyzer.cpp
    Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp
    Core/ProcessInteraction/EducationalDylibFramework.cpp
    Core/ProcessInteraction/RobloxInjector.cpp
    Core/Integration/SecureIntegrationFramework.cpp
    Core/Logging/SecurityLogger.cpp
    Core/Compliance/SecurityComplianceValidator.cpp
)

target_include_directories(RobloxResearchCore PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/Core
    ${LUAJIT_INCLUDE_DIR}
)

target_link_libraries(RobloxResearchCore
    ${LUAJIT_LIBRARY}
    "-framework Foundation"
    "-framework Security"
    "-framework CoreFoundation"
)

# Compiler flags for security
target_compile_options(RobloxResearchCore PRIVATE
    -Wall -Wextra -Werror
    -fstack-protector-strong
    -D_FORTIFY_SOURCE=2
    -fPIE
)

# Test executable
add_executable(ResearchTests
    Tests/ProcessMonitorTests.cpp
    Tests/LuaRuntimeTests.cpp
    Tests/SecurityValidationTests.cpp
    Tests/main.cpp
)

target_link_libraries(ResearchTests RobloxResearchCore)

# Comprehensive test suite
add_executable(ComprehensiveTestSuite
    Tests/ComprehensiveTestSuite.cpp
    Tests/test_main.cpp
)

target_link_libraries(ComprehensiveTestSuite RobloxResearchCore)

# Comprehensive framework test
add_executable(ComprehensiveTest
    Tests/ComprehensiveFrameworkTest.cpp
)

target_link_libraries(ComprehensiveTest RobloxResearchCore)

# Roblox Executor main application
add_executable(RobloxExecutor
    RobloxExecutor.cpp
)

target_link_libraries(RobloxExecutor RobloxResearchCore)

# Roblox Injector tests
add_executable(RobloxInjectorTests
    Tests/RobloxInjectorTests.cpp
)

target_link_libraries(RobloxInjectorTests RobloxResearchCore)

# Security validation
add_custom_target(security_check
    COMMAND codesign --verify --deep --strict ${CMAKE_BINARY_DIR}/ResearchTests
    DEPENDS ResearchTests
    COMMENT "Validating code signing and security compliance"
)

# Documentation generation
find_program(DOXYGEN_EXECUTABLE doxygen)
if(DOXYGEN_EXECUTABLE)
    add_custom_target(docs
        COMMAND ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile
        WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
        COMMENT "Generating documentation"
    )
endif()
