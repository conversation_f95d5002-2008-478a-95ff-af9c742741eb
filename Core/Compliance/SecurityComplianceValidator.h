#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <functional>

namespace RobloxResearch {
namespace Compliance {

/**
 * @brief Comprehensive security compliance validation system
 * 
 * This class provides automated validation of security compliance,
 * continuous monitoring, and automated testing capabilities for the
 * research framework.
 */
class SecurityComplianceValidator {
public:
    enum class ComplianceLevel {
        CRITICAL,
        HIGH,
        MEDIUM,
        LOW,
        INFORMATIONAL
    };

    enum class ComplianceStatus {
        COMPLIANT,
        NON_COMPLIANT,
        PARTIALLY_COMPLIANT,
        UNKNOWN,
        NOT_APPLICABLE
    };

    struct ComplianceRule {
        std::string ruleId;
        std::string name;
        std::string description;
        ComplianceLevel level;
        std::function<ComplianceStatus()> validator;
        std::string remediation;
        bool mandatory;
    };

    struct ComplianceResult {
        std::string ruleId;
        std::string ruleName;
        ComplianceStatus status;
        ComplianceLevel level;
        std::string message;
        std::string evidence;
        std::string remediation;
        std::chrono::system_clock::time_point timestamp;
    };

    struct ComplianceReport {
        std::string reportId;
        std::chrono::system_clock::time_point generationTime;
        std::vector<ComplianceResult> results;
        size_t totalRules;
        size_t compliantRules;
        size_t nonCompliantRules;
        size_t criticalViolations;
        double complianceScore;
        std::string overallStatus;
        std::vector<std::string> recommendations;
    };

    struct ContinuousMonitoringConfig {
        std::chrono::seconds checkInterval;
        bool enableRealTimeAlerts;
        bool enableAutomaticRemediation;
        std::vector<std::string> monitoredRules;
        std::string alertEndpoint;
    };

    SecurityComplianceValidator();
    ~SecurityComplianceValidator();

    /**
     * @brief Initialize compliance validation system
     * @return true if initialization successful
     */
    bool initialize();

    /**
     * @brief Register a compliance rule
     * @param rule Compliance rule to register
     * @return true if rule registered successfully
     */
    bool registerRule(const ComplianceRule& rule);

    /**
     * @brief Run all compliance checks
     * @return Comprehensive compliance report
     */
    ComplianceReport runComplianceCheck();

    /**
     * @brief Run specific compliance rule
     * @param ruleId ID of rule to run
     * @return Compliance result for the rule
     */
    ComplianceResult runSpecificRule(const std::string& ruleId);

    /**
     * @brief Start continuous monitoring
     * @param config Monitoring configuration
     * @return true if monitoring started successfully
     */
    bool startContinuousMonitoring(const ContinuousMonitoringConfig& config);

    /**
     * @brief Stop continuous monitoring
     */
    void stopContinuousMonitoring();

    /**
     * @brief Get compliance history
     * @param hours Number of hours of history to retrieve
     * @return Vector of historical compliance reports
     */
    std::vector<ComplianceReport> getComplianceHistory(size_t hours = 24);

    /**
     * @brief Generate compliance dashboard data
     * @return JSON string with dashboard data
     */
    std::string generateComplianceDashboard();

    /**
     * @brief Export compliance report
     * @param report Report to export
     * @param filename Output filename
     * @param format Export format (json, html, pdf, csv)
     * @return true if export successful
     */
    bool exportComplianceReport(const ComplianceReport& report, 
                               const std::string& filename, 
                               const std::string& format);

    /**
     * @brief Set compliance threshold
     * @param threshold Minimum compliance score (0.0 - 1.0)
     */
    void setComplianceThreshold(double threshold);

    /**
     * @brief Get current compliance status
     * @return Current overall compliance status
     */
    ComplianceStatus getCurrentComplianceStatus();

    /**
     * @brief Register compliance violation callback
     * @param callback Function to call when violation detected
     */
    void registerViolationCallback(std::function<void(const ComplianceResult&)> callback);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Built-in compliance rules
    void registerBuiltInRules();
    ComplianceStatus validateSIPEnabled();
    ComplianceStatus validateCodeSigningIntegrity();
    ComplianceStatus validateSandboxConfiguration();
    ComplianceStatus validateMemoryProtection();
    ComplianceStatus validateNetworkSecurity();
    ComplianceStatus validateLoggingConfiguration();
    ComplianceStatus validateUserPermissions();
    ComplianceStatus validateSystemIntegrity();
};

/**
 * @brief Automated testing framework for security compliance
 */
class AutomatedSecurityTester {
public:
    struct TestCase {
        std::string testId;
        std::string name;
        std::string description;
        std::function<bool()> testFunction;
        std::vector<std::string> prerequisites;
        std::chrono::seconds timeout;
        bool critical;
    };

    struct TestResult {
        std::string testId;
        std::string testName;
        bool passed;
        std::string message;
        std::string details;
        std::chrono::milliseconds executionTime;
        std::chrono::system_clock::time_point timestamp;
    };

    struct TestSuite {
        std::string suiteId;
        std::string name;
        std::vector<TestCase> tests;
        std::vector<std::string> setupSteps;
        std::vector<std::string> teardownSteps;
    };

    struct TestReport {
        std::string reportId;
        std::chrono::system_clock::time_point executionTime;
        std::vector<TestResult> results;
        size_t totalTests;
        size_t passedTests;
        size_t failedTests;
        size_t criticalFailures;
        std::chrono::milliseconds totalExecutionTime;
        std::string overallStatus;
    };

    /**
     * @brief Register a test case
     * @param testCase Test case to register
     * @return true if registration successful
     */
    static bool registerTestCase(const TestCase& testCase);

    /**
     * @brief Register a test suite
     * @param testSuite Test suite to register
     * @return true if registration successful
     */
    static bool registerTestSuite(const TestSuite& testSuite);

    /**
     * @brief Run all registered tests
     * @return Comprehensive test report
     */
    static TestReport runAllTests();

    /**
     * @brief Run specific test suite
     * @param suiteId ID of test suite to run
     * @return Test report for the suite
     */
    static TestReport runTestSuite(const std::string& suiteId);

    /**
     * @brief Run specific test case
     * @param testId ID of test case to run
     * @return Test result
     */
    static TestResult runTestCase(const std::string& testId);

    /**
     * @brief Generate test report
     * @param report Test report to format
     * @return Formatted test report string
     */
    static std::string generateTestReport(const TestReport& report);

    /**
     * @brief Register built-in security tests
     */
    static void registerBuiltInTests();

private:
    // Built-in test implementations
    static bool testLuaSandboxSecurity();
    static bool testProcessIsolation();
    static bool testMemoryProtection();
    static bool testCodeSigningValidation();
    static bool testSIPEnforcement();
    static bool testNetworkSecurity();
    static bool testFileSystemSecurity();
    static bool testPrivilegeEscalation();
    static bool testInjectionPrevention();
    static bool testCryptographicSecurity();
};

/**
 * @brief Security baseline configuration validator
 */
class SecurityBaselineValidator {
public:
    struct BaselineConfiguration {
        std::string configId;
        std::string name;
        std::string description;
        std::vector<std::string> requiredSettings;
        std::vector<std::string> prohibitedSettings;
        std::vector<std::string> recommendedSettings;
    };

    struct BaselineResult {
        std::string configId;
        bool compliant;
        std::vector<std::string> missingSettings;
        std::vector<std::string> prohibitedFound;
        std::vector<std::string> recommendations;
        double complianceScore;
    };

    /**
     * @brief Validate system against security baseline
     * @param baseline Baseline configuration to validate against
     * @return Baseline validation result
     */
    static BaselineResult validateBaseline(const BaselineConfiguration& baseline);

    /**
     * @brief Get macOS security baseline
     * @return Standard macOS security baseline configuration
     */
    static BaselineConfiguration getMacOSSecurityBaseline();

    /**
     * @brief Get research framework baseline
     * @return Security baseline for research framework
     */
    static BaselineConfiguration getResearchFrameworkBaseline();

    /**
     * @brief Generate baseline compliance report
     * @param results Vector of baseline results
     * @return Formatted compliance report
     */
    static std::string generateBaselineReport(const std::vector<BaselineResult>& results);
};

/**
 * @brief Real-time security monitoring system
 */
class RealTimeSecurityMonitor {
public:
    struct SecurityAlert {
        std::string alertId;
        std::string alertType;
        std::string severity;
        std::string description;
        std::string source;
        std::chrono::system_clock::time_point timestamp;
        bool acknowledged;
        std::string response;
    };

    struct MonitoringMetrics {
        size_t totalAlerts;
        size_t criticalAlerts;
        size_t acknowledgedAlerts;
        std::chrono::system_clock::time_point lastAlert;
        double averageResponseTime;
        std::vector<std::string> topAlertTypes;
    };

    /**
     * @brief Start real-time security monitoring
     * @return true if monitoring started successfully
     */
    static bool startMonitoring();

    /**
     * @brief Stop real-time security monitoring
     */
    static void stopMonitoring();

    /**
     * @brief Register security alert handler
     * @param handler Function to handle security alerts
     */
    static void registerAlertHandler(std::function<void(const SecurityAlert&)> handler);

    /**
     * @brief Get recent security alerts
     * @param hours Number of hours of alerts to retrieve
     * @return Vector of recent alerts
     */
    static std::vector<SecurityAlert> getRecentAlerts(size_t hours = 24);

    /**
     * @brief Acknowledge security alert
     * @param alertId ID of alert to acknowledge
     * @param response Response message
     * @return true if acknowledgment successful
     */
    static bool acknowledgeAlert(const std::string& alertId, const std::string& response);

    /**
     * @brief Get monitoring metrics
     * @return Current monitoring metrics
     */
    static MonitoringMetrics getMetrics();

    /**
     * @brief Generate security dashboard
     * @return JSON string with dashboard data
     */
    static std::string generateSecurityDashboard();
};

/**
 * @brief Compliance validation macros for convenience
 */
#define VALIDATE_COMPLIANCE(validator, ruleId) \
    validator.runSpecificRule(ruleId)

#define REGISTER_COMPLIANCE_RULE(validator, rule) \
    validator.registerRule(rule)

#define CHECK_SIP_COMPLIANCE() \
    SecurityComplianceValidator::validateSIPEnabled()

#define CHECK_SANDBOX_COMPLIANCE() \
    SecurityComplianceValidator::validateSandboxConfiguration()

#define RUN_SECURITY_TEST(testId) \
    AutomatedSecurityTester::runTestCase(testId)

#define VALIDATE_SECURITY_BASELINE(baseline) \
    SecurityBaselineValidator::validateBaseline(baseline)

} // namespace Compliance
} // namespace RobloxResearch
