#include "SIPCompliantTools.h"
#include <sys/sysctl.h>
#include <iostream>

namespace RobloxResearch {
namespace SecurityAnalysis {

SIPCompliantTools::SIPCompliantTools() {}

SIPCompliantTools::~SIPCompliantTools() {}

bool SIPCompliantTools::isSIPEnabled() {
    int sipStatus = 0;
    size_t size = sizeof(sipStatus);
    
    if (sysctlbyname("kern.sip_status", &sipStatus, &size, nullptr, 0) == 0) {
        return sipStatus != 0;
    }
    
    return false;
}

SIPCompliantTools::SIPStatus SIPCompliantTools::getSIPStatus() {
    SIPStatus status;
    status.enabled = isSIPEnabled();
    status.statusCode = 0;
    
    int sipStatusCode = 0;
    size_t size = sizeof(sipStatusCode);
    
    if (sysctlbyname("kern.sip_status", &sipStatusCode, &size, nullptr, 0) == 0) {
        status.statusCode = sipStatusCode;
    }
    
    return status;
}

std::vector<std::string> SIPCompliantTools::validateSIPCompliance() {
    std::vector<std::string> violations;
    
    if (!isSIPEnabled()) {
        violations.push_back("System Integrity Protection is disabled");
        violations.push_back("Research results may not be representative of secure macOS environment");
    }
    
    return violations;
}

std::string SIPCompliantTools::generateSIPReport() {
    std::string report = "=== System Integrity Protection Report ===\n";
    
    auto status = getSIPStatus();
    
    report += "SIP Status: " + std::string(status.enabled ? "ENABLED" : "DISABLED") + "\n";
    report += "Status Code: " + std::to_string(status.statusCode) + "\n";
    
    auto violations = validateSIPCompliance();
    if (violations.empty()) {
        report += "Compliance: VALID - No SIP violations detected\n";
        report += "Security Level: HIGH - System integrity maintained\n";
    } else {
        report += "Compliance: INVALID - SIP violations detected\n";
        report += "Security Level: COMPROMISED\n";
        report += "Violations:\n";
        for (const auto& violation : violations) {
            report += "  - " + violation + "\n";
        }
    }
    
    return report;
}

bool SIPCompliantTools::canPerformOperation(const std::string& operation) {
    // All operations must be SIP-compliant
    if (!isSIPEnabled()) {
        return false;
    }
    
    // Define allowed operations
    static const std::vector<std::string> allowedOperations = {
        "process_enumeration",
        "memory_reading",
        "security_analysis",
        "lua_execution",
        "logging",
        "reporting"
    };
    
    return std::find(allowedOperations.begin(), allowedOperations.end(), operation) != allowedOperations.end();
}

} // namespace SecurityAnalysis
} // namespace RobloxResearch
