#pragma once

#include <string>
#include <vector>
#include <algorithm>

namespace RobloxResearch {
namespace SecurityAnalysis {

/**
 * @brief SIP-compliant security analysis tools
 */
class SIPCompliantTools {
public:
    struct SIPStatus {
        bool enabled;
        int statusCode;
    };

    SIPCompliantTools();
    ~SIPCompliantTools();

    /**
     * @brief Check if System Integrity Protection is enabled
     * @return true if SIP is enabled
     */
    bool isSIPEnabled();

    /**
     * @brief Get detailed SIP status
     * @return SIP status information
     */
    SIPStatus getSIPStatus();

    /**
     * @brief Validate SIP compliance
     * @return Vector of compliance violations
     */
    std::vector<std::string> validateSIPCompliance();

    /**
     * @brief Generate SIP compliance report
     * @return Formatted SIP report
     */
    std::string generateSIPReport();

    /**
     * @brief Check if an operation can be performed under SIP
     * @param operation Operation to check
     * @return true if operation is allowed
     */
    bool canPerformOperation(const std::string& operation);
};

} // namespace SecurityAnalysis
} // namespace RobloxResearch
