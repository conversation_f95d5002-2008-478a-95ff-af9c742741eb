#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>

extern "C" {
#include <luajit.h>
#include <lualib.h>
#include <lauxlib.h>
}

namespace RobloxResearch {
namespace LuaRuntime {

/**
 * @brief Sandboxed Lua execution environment for security research
 * 
 * This class provides a secure, isolated Lua runtime environment that:
 * - Runs scripts in a restricted sandbox
 * - Prevents access to dangerous system functions
 * - Monitors resource usage and execution time
 * - Logs all operations for security analysis
 */
class SandboxedLuaEngine {
public:
    enum class ExecutionResult {
        Success,
        SyntaxError,
        RuntimeError,
        TimeoutError,
        SecurityViolation,
        ResourceLimitExceeded
    };

    struct ExecutionContext {
        std::string script;
        std::chrono::milliseconds timeout{5000}; // 5 second default timeout
        size_t memoryLimit{10 * 1024 * 1024}; // 10MB default limit
        bool allowFileAccess{false};
        bool allowNetworkAccess{false};
        bool allowSystemCalls{false};
    };

    struct ExecutionReport {
        ExecutionResult result;
        std::string output;
        std::string errorMessage;
        std::chrono::milliseconds executionTime;
        size_t memoryUsed;
        std::vector<std::string> securityViolations;
        std::vector<std::string> apiCalls;
    };

    SandboxedLuaEngine();
    ~SandboxedLuaEngine();

    /**
     * @brief Execute Lua script in sandboxed environment
     * @param context Execution parameters and security constraints
     * @return Detailed execution report
     */
    ExecutionReport executeScript(const ExecutionContext& context);

    /**
     * @brief Validate script for security issues before execution
     * @param script Lua script to validate
     * @return Vector of security concerns found
     */
    std::vector<std::string> validateScriptSecurity(const std::string& script);

    /**
     * @brief Get list of available safe functions in sandbox
     * @return Vector of function names available to scripts
     */
    std::vector<std::string> getAvailableFunctions() const;

    /**
     * @brief Add custom safe function to sandbox
     * @param name Function name
     * @param func C++ function to expose
     * @return true if function was added successfully
     */
    bool addSafeFunction(const std::string& name, lua_CFunction func);

    /**
     * @brief Reset sandbox to clean state
     */
    void resetSandbox();

    /**
     * @brief Get security compliance status
     * @return true if sandbox is properly configured
     */
    bool validateSandboxSecurity() const;

    // Enhanced security testing capabilities
    struct SecurityTestResult {
        bool passed;
        std::string testName;
        std::string description;
        std::vector<std::string> violations;
        std::chrono::milliseconds executionTime;
    };

    struct ComprehensiveSecurityReport {
        std::vector<SecurityTestResult> testResults;
        bool overallSecurityValid;
        std::string securityLevel;
        std::vector<std::string> recommendations;
        std::chrono::system_clock::time_point reportTime;
    };

    /**
     * @brief Run comprehensive security test suite
     * @return Detailed security test results
     */
    ComprehensiveSecurityReport runSecurityTestSuite();

    /**
     * @brief Test specific security boundary
     * @param testType Type of security test to run
     * @return Security test result
     */
    SecurityTestResult testSecurityBoundary(const std::string& testType);

    /**
     * @brief Monitor runtime security violations in real-time
     * @param duration How long to monitor
     * @return Vector of detected violations
     */
    std::vector<std::string> monitorSecurityViolations(std::chrono::seconds duration);

    /**
     * @brief Stress test the sandbox with malicious scripts
     * @return Results of stress testing
     */
    std::vector<SecurityTestResult> stressTestSandbox();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Security validation methods
    bool isScriptSafe(const std::string& script) const;
    void setupSandboxEnvironment(lua_State* L);
    void removeDangerousFunctions(lua_State* L);
    void installSecurityHooks(lua_State* L);
    void setupResourceLimits(lua_State* L, const ExecutionContext& context);
};

/**
 * @brief Security wrapper for Lua function calls
 */
class SecurityWrapper {
public:
    /**
     * @brief Wrap a C++ function for safe exposure to Lua
     * @param func Function to wrap
     * @return Wrapped function with security checks
     */
    static lua_CFunction wrapFunction(std::function<int(lua_State*)> func);

    /**
     * @brief Check if function call is allowed in current security context
     * @param functionName Name of function being called
     * @return true if call is permitted
     */
    static bool isFunctionCallAllowed(const std::string& functionName);

    /**
     * @brief Log function call for security audit
     * @param functionName Function that was called
     * @param args Arguments passed to function
     */
    static void logFunctionCall(const std::string& functionName, 
                               const std::vector<std::string>& args);
};

/**
 * @brief Resource monitor for Lua execution
 */
class ResourceMonitor {
public:
    ResourceMonitor(size_t memoryLimit, std::chrono::milliseconds timeLimit);
    ~ResourceMonitor();

    /**
     * @brief Check if resource limits are exceeded
     * @return true if execution should continue
     */
    bool checkLimits() const;

    /**
     * @brief Get current memory usage
     * @return Memory used in bytes
     */
    size_t getCurrentMemoryUsage() const;

    /**
     * @brief Get elapsed execution time
     * @return Time elapsed since monitoring started
     */
    std::chrono::milliseconds getElapsedTime() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace LuaRuntime
} // namespace RobloxResearch
