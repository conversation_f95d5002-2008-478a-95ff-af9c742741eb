#include "SecurityWrapper.h"
#include <iostream>

namespace RobloxResearch {
namespace LuaRuntime {

SecurityWrapper::SecurityWrapper() {}

SecurityWrapper::~SecurityWrapper() {}

bool SecurityWrapper::validateFunction(const std::string& functionName) {
    // List of allowed functions
    static const std::vector<std::string> allowedFunctions = {
        "print", "tostring", "tonumber", "type", "pairs", "ipairs",
        "next", "select", "unpack", "pcall", "xpcall",
        "math.abs", "math.acos", "math.asin", "math.atan", "math.atan2",
        "math.ceil", "math.cos", "math.cosh", "math.deg", "math.exp",
        "math.floor", "math.fmod", "math.frexp", "math.huge", "math.ldexp",
        "math.log", "math.log10", "math.max", "math.min", "math.modf",
        "math.pi", "math.pow", "math.rad", "math.random", "math.randomseed",
        "math.sin", "math.sinh", "math.sqrt", "math.tan", "math.tanh",
        "string.byte", "string.char", "string.find", "string.format",
        "string.gmatch", "string.gsub", "string.len", "string.lower",
        "string.match", "string.rep", "string.reverse", "string.sub",
        "string.upper", "table.concat", "table.insert", "table.maxn",
        "table.remove", "table.sort"
    };
    
    return std::find(allowedFunctions.begin(), allowedFunctions.end(), functionName) != allowedFunctions.end();
}

std::vector<std::string> SecurityWrapper::getBlockedFunctions() {
    static const std::vector<std::string> blockedFunctions = {
        "os.execute", "os.exit", "os.getenv", "os.remove", "os.rename",
        "os.tmpname", "io.open", "io.close", "io.read", "io.write",
        "io.popen", "io.tmpfile", "loadfile", "dofile", "require",
        "debug.debug", "debug.getfenv", "debug.gethook", "debug.getinfo",
        "debug.getlocal", "debug.getmetatable", "debug.getregistry",
        "debug.getupvalue", "debug.setfenv", "debug.sethook",
        "debug.setlocal", "debug.setmetatable", "debug.setupvalue",
        "debug.traceback", "package.loadlib", "package.seeall"
    };
    
    return blockedFunctions;
}

bool SecurityWrapper::isSecureScript(const std::string& script) {
    auto blockedFunctions = getBlockedFunctions();
    
    for (const auto& blocked : blockedFunctions) {
        if (script.find(blocked) != std::string::npos) {
            return false;
        }
    }
    
    return true;
}

std::vector<std::string> SecurityWrapper::scanForViolations(const std::string& script) {
    std::vector<std::string> violations;
    auto blockedFunctions = getBlockedFunctions();
    
    for (const auto& blocked : blockedFunctions) {
        if (script.find(blocked) != std::string::npos) {
            violations.push_back("Blocked function detected: " + blocked);
        }
    }
    
    return violations;
}

std::string SecurityWrapper::sanitizeScript(const std::string& script) {
    std::string sanitized = script;
    auto blockedFunctions = getBlockedFunctions();
    
    for (const auto& blocked : blockedFunctions) {
        size_t pos = 0;
        while ((pos = sanitized.find(blocked, pos)) != std::string::npos) {
            sanitized.replace(pos, blocked.length(), "-- BLOCKED: " + blocked);
            pos += blocked.length() + 11; // Length of "-- BLOCKED: "
        }
    }
    
    return sanitized;
}

} // namespace LuaRuntime
} // namespace RobloxResearch
