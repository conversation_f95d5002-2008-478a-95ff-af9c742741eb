#pragma once

#include <string>
#include <vector>
#include <algorithm>

namespace RobloxResearch {
namespace LuaRuntime {

/**
 * @brief Security wrapper for Lua script validation
 */
class SecurityWrapper {
public:
    SecurityWrapper();
    ~SecurityWrapper();

    /**
     * @brief Validate if a function is allowed
     * @param functionName Name of the function to validate
     * @return true if function is allowed
     */
    bool validateFunction(const std::string& functionName);

    /**
     * @brief Get list of blocked functions
     * @return Vector of blocked function names
     */
    std::vector<std::string> getBlockedFunctions();

    /**
     * @brief Check if a script is secure
     * @param script Script content to check
     * @return true if script is secure
     */
    bool isSecureScript(const std::string& script);

    /**
     * @brief Scan script for security violations
     * @param script Script content to scan
     * @return Vector of violations found
     */
    std::vector<std::string> scanForViolations(const std::string& script);

    /**
     * @brief Sanitize script by removing dangerous functions
     * @param script Script content to sanitize
     * @return Sanitized script content
     */
    std::string sanitizeScript(const std::string& script);
};

} // namespace LuaRuntime
} // namespace RobloxResearch
