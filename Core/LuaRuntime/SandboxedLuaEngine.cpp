#include "SandboxedLuaEngine.h"
#include <iostream>
#include <sstream>
#include <regex>
#include <thread>
#include <atomic>

namespace RobloxResearch {
namespace LuaRuntime {

// Global security state for Lu<PERSON> hooks
static std::atomic<bool> g_securityViolationDetected{false};
static std::vector<std::string> g_securityViolations;
static std::vector<std::string> g_apiCalls;

// Security hook for Lua execution
static void securityHook(lua_State* L, lua_Debug* ar) {
    // Monitor for suspicious operations
    if (ar->event == LUA_HOOKCALL) {
        lua_getinfo(L, "n", ar);
        if (ar->name) {
            g_apiCalls.push_back(std::string(ar->name));
            
            // Check for dangerous function calls
            std::string funcName(ar->name);
            if (funcName == "os" || funcName == "io" || funcName == "debug" ||
                funcName == "package" || funcName == "require") {
                g_securityViolationDetected = true;
                g_securityViolations.push_back("Attempted call to restricted function: " + funcName);
            }
        }
    }
}

// Memory allocation hook for resource monitoring
static void* secureAllocator(void* ud, void* ptr, size_t /* osize */, size_t nsize) {
    ResourceMonitor* monitor = static_cast<ResourceMonitor*>(ud);
    
    if (nsize == 0) {
        free(ptr);
        return nullptr;
    }
    
    // Check memory limits before allocation
    if (monitor && !monitor->checkLimits()) {
        return nullptr; // Allocation denied due to limits
    }
    
    return realloc(ptr, nsize);
}

class SandboxedLuaEngine::Impl {
public:
    lua_State* L;
    std::unique_ptr<ResourceMonitor> monitor;
    
    Impl() : L(nullptr) {
        resetLuaState();
    }
    
    ~Impl() {
        if (L) {
            lua_close(L);
        }
    }
    
    void resetLuaState() {
        if (L) {
            lua_close(L);
        }
        
        // Create new Lua state with custom allocator for monitoring
        L = lua_newstate(secureAllocator, nullptr);
        if (!L) {
            throw std::runtime_error("Failed to create Lua state");
        }
        
        // Load only safe libraries
        luaL_openlibs(L);
        
        // Remove dangerous functions and setup sandbox
        removeDangerousFunctions();
        setupSandboxEnvironment();
    }
    
private:
    void removeDangerousFunctions() {
        // Remove dangerous global functions
        const char* dangerousFunctions[] = {
            "os", "io", "debug", "package", "require", "dofile", "loadfile",
            "load", "loadstring", "rawget", "rawset", "rawequal", "rawlen",
            "getmetatable", "setmetatable", nullptr
        };
        
        for (int i = 0; dangerousFunctions[i]; i++) {
            lua_pushnil(L);
            lua_setglobal(L, dangerousFunctions[i]);
        }
    }
    
    void setupSandboxEnvironment() {
        // Create restricted environment
        lua_newtable(L); // Create new environment table
        
        // Add safe functions
        addSafeMathFunctions();
        addSafeStringFunctions();
        addSafeTableFunctions();
        addResearchFunctions();
        
        // Set as global environment
        lua_pushvalue(L, -1);
        lua_setglobal(L, "_G");
    }
    
    void addSafeMathFunctions() {
        lua_getglobal(L, "math");
        if (lua_istable(L, -1)) {
            lua_setfield(L, -2, "math");
        } else {
            lua_pop(L, 1);
        }
    }
    
    void addSafeStringFunctions() {
        lua_getglobal(L, "string");
        if (lua_istable(L, -1)) {
            lua_setfield(L, -2, "string");
        } else {
            lua_pop(L, 1);
        }
    }
    
    void addSafeTableFunctions() {
        lua_getglobal(L, "table");
        if (lua_istable(L, -1)) {
            lua_setfield(L, -2, "table");
        } else {
            lua_pop(L, 1);
        }
    }
    
    void addResearchFunctions() {
        // Add custom research functions
        lua_pushcfunction(L, researchPrint);
        lua_setfield(L, -2, "print");
        
        lua_pushcfunction(L, researchLog);
        lua_setfield(L, -2, "log");
    }
    
    static int researchPrint(lua_State* L) {
        int n = lua_gettop(L);
        std::ostringstream output;
        
        for (int i = 1; i <= n; i++) {
            if (i > 1) output << "\t";
            
            size_t len;
            const char* s = lua_tolstring(L, i, &len);
            output << s;
            lua_pop(L, 1);
        }
        
        std::cout << "[Lua Output] " << output.str() << std::endl;
        return 0;
    }
    
    static int researchLog(lua_State* L) {
        const char* message = luaL_checkstring(L, 1);
        std::cout << "[Lua Log] " << message << std::endl;
        return 0;
    }
};

SandboxedLuaEngine::SandboxedLuaEngine() : pImpl(std::make_unique<Impl>()) {}

SandboxedLuaEngine::~SandboxedLuaEngine() = default;

SandboxedLuaEngine::ExecutionReport SandboxedLuaEngine::executeScript(const ExecutionContext& context) {
    ExecutionReport report;
    auto startTime = std::chrono::steady_clock::now();
    
    // Reset security state
    g_securityViolationDetected = false;
    g_securityViolations.clear();
    g_apiCalls.clear();
    
    // Validate script security first
    auto securityIssues = validateScriptSecurity(context.script);
    if (!securityIssues.empty()) {
        report.result = ExecutionResult::SecurityViolation;
        report.securityViolations = securityIssues;
        return report;
    }
    
    // Setup resource monitoring
    pImpl->monitor = std::make_unique<ResourceMonitor>(context.memoryLimit, context.timeout);
    
    // Install security hooks
    lua_sethook(pImpl->L, securityHook, LUA_MASKCALL | LUA_MASKRET, 0);
    
    // Execute script with timeout
    std::atomic<bool> executionComplete{false};
    std::string output;
    std::string errorMessage;
    ExecutionResult result = ExecutionResult::Success;
    
    std::thread executionThread([&]() {
        // Redirect output capture
        std::ostringstream capturedOutput;
        
        // Execute the script
        int loadResult = luaL_loadstring(pImpl->L, context.script.c_str());
        if (loadResult != LUA_OK) {
            result = ExecutionResult::SyntaxError;
            errorMessage = lua_tostring(pImpl->L, -1);
            lua_pop(pImpl->L, 1);
        } else {
            int execResult = lua_pcall(pImpl->L, 0, LUA_MULTRET, 0);
            if (execResult != LUA_OK) {
                result = ExecutionResult::RuntimeError;
                errorMessage = lua_tostring(pImpl->L, -1);
                lua_pop(pImpl->L, 1);
            }
        }
        
        output = capturedOutput.str();
        executionComplete = true;
    });
    
    // Wait for completion or timeout
    if (executionThread.joinable()) {
        auto timeout = std::chrono::steady_clock::now() + context.timeout;
        
        while (!executionComplete && std::chrono::steady_clock::now() < timeout) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
            // Check resource limits
            if (pImpl->monitor && !pImpl->monitor->checkLimits()) {
                result = ExecutionResult::ResourceLimitExceeded;
                break;
            }
            
            // Check for security violations
            if (g_securityViolationDetected) {
                result = ExecutionResult::SecurityViolation;
                break;
            }
        }
        
        if (!executionComplete) {
            result = ExecutionResult::TimeoutError;
            // Note: In a real implementation, we'd need to safely terminate the Lua execution
        }
        
        executionThread.join();
    }
    
    auto endTime = std::chrono::steady_clock::now();
    
    // Populate report
    report.result = result;
    report.output = output;
    report.errorMessage = errorMessage;
    report.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    report.memoryUsed = pImpl->monitor ? pImpl->monitor->getCurrentMemoryUsage() : 0;
    report.securityViolations = g_securityViolations;
    report.apiCalls = g_apiCalls;
    
    return report;
}

std::vector<std::string> SandboxedLuaEngine::validateScriptSecurity(const std::string& script) {
    std::vector<std::string> violations;
    
    // Check for dangerous patterns using regex
    std::vector<std::pair<std::regex, std::string>> dangerousPatterns = {
        {std::regex(R"(\bos\.)"), "Attempted access to os library"},
        {std::regex(R"(\bio\.)"), "Attempted access to io library"},
        {std::regex(R"(\bdebug\.)"), "Attempted access to debug library"},
        {std::regex(R"(\bpackage\.)"), "Attempted access to package library"},
        {std::regex(R"(\brequire\s*\()"), "Attempted to require external modules"},
        {std::regex(R"(\bdofile\s*\()"), "Attempted to execute external file"},
        {std::regex(R"(\bloadfile\s*\()"), "Attempted to load external file"},
        {std::regex(R"(\bgetmetatable\s*\()"), "Attempted metatable manipulation"},
        {std::regex(R"(\bsetmetatable\s*\()"), "Attempted metatable manipulation"},
        {std::regex(R"(\brawget\s*\()"), "Attempted raw table access"},
        {std::regex(R"(\brawset\s*\()"), "Attempted raw table modification"}
    };
    
    for (const auto& pattern : dangerousPatterns) {
        if (std::regex_search(script, pattern.first)) {
            violations.push_back(pattern.second);
        }
    }
    
    return violations;
}

std::vector<std::string> SandboxedLuaEngine::getAvailableFunctions() const {
    return {
        "print", "log", "type", "tostring", "tonumber",
        "math.*", "string.*", "table.*",
        "pairs", "ipairs", "next", "select"
    };
}

bool SandboxedLuaEngine::addSafeFunction(const std::string& name, lua_CFunction func) {
    if (!pImpl->L) return false;
    
    // Wrap function with security checks
    lua_pushcfunction(pImpl->L, func);
    lua_setglobal(pImpl->L, name.c_str());
    
    return true;
}

void SandboxedLuaEngine::resetSandbox() {
    pImpl->resetLuaState();
}

bool SandboxedLuaEngine::validateSandboxSecurity() const {
    // Verify that dangerous functions are not accessible
    if (!pImpl->L) return false;
    
    const char* dangerousFunctions[] = {"os", "io", "debug", "package", nullptr};
    
    for (int i = 0; dangerousFunctions[i]; i++) {
        lua_getglobal(pImpl->L, dangerousFunctions[i]);
        if (!lua_isnil(pImpl->L, -1)) {
            lua_pop(pImpl->L, 1);
            return false; // Dangerous function is accessible
        }
        lua_pop(pImpl->L, 1);
    }
    
    return true;
}

// ResourceMonitor implementation
class ResourceMonitor::Impl {
public:
    size_t memoryLimit;
    std::chrono::milliseconds timeLimit;
    std::chrono::steady_clock::time_point startTime;
    
    Impl(size_t memLimit, std::chrono::milliseconds timeLimit)
        : memoryLimit(memLimit), timeLimit(timeLimit), startTime(std::chrono::steady_clock::now()) {}
};

ResourceMonitor::ResourceMonitor(size_t memoryLimit, std::chrono::milliseconds timeLimit)
    : pImpl(std::make_unique<Impl>(memoryLimit, timeLimit)) {}

ResourceMonitor::~ResourceMonitor() = default;

bool ResourceMonitor::checkLimits() const {
    // Check time limit
    auto elapsed = getElapsedTime();
    if (elapsed > pImpl->timeLimit) {
        return false;
    }
    
    // Check memory limit (simplified - would need more sophisticated tracking)
    auto memUsage = getCurrentMemoryUsage();
    if (memUsage > pImpl->memoryLimit) {
        return false;
    }
    
    return true;
}

size_t ResourceMonitor::getCurrentMemoryUsage() const {
    // Simplified memory usage calculation
    // In a real implementation, this would track Lua memory usage more precisely
    return 0;
}

std::chrono::milliseconds ResourceMonitor::getElapsedTime() const {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now - pImpl->startTime);
}

// Enhanced security testing implementations
SandboxedLuaEngine::ComprehensiveSecurityReport SandboxedLuaEngine::runSecurityTestSuite() {
    ComprehensiveSecurityReport report;
    report.reportTime = std::chrono::system_clock::now();

    // Test 1: Dangerous function access prevention
    auto test1 = testSecurityBoundary("dangerous_functions");
    report.testResults.push_back(test1);

    // Test 2: File system access prevention
    auto test2 = testSecurityBoundary("file_access");
    report.testResults.push_back(test2);

    // Test 3: Network access prevention
    auto test3 = testSecurityBoundary("network_access");
    report.testResults.push_back(test3);

    // Test 4: System call prevention
    auto test4 = testSecurityBoundary("system_calls");
    report.testResults.push_back(test4);

    // Test 5: Memory limit enforcement
    auto test5 = testSecurityBoundary("memory_limits");
    report.testResults.push_back(test5);

    // Test 6: Timeout enforcement
    auto test6 = testSecurityBoundary("timeout_limits");
    report.testResults.push_back(test6);

    // Test 7: Code injection prevention
    auto test7 = testSecurityBoundary("code_injection");
    report.testResults.push_back(test7);

    // Assess overall security
    bool allPassed = true;
    for (const auto& test : report.testResults) {
        if (!test.passed) {
            allPassed = false;
            break;
        }
    }

    report.overallSecurityValid = allPassed;
    report.securityLevel = allPassed ? "Maximum" : "Compromised";

    // Generate recommendations
    if (allPassed) {
        report.recommendations.push_back("Sandbox security is optimal");
        report.recommendations.push_back("Continue monitoring for new threats");
    } else {
        report.recommendations.push_back("Review failed security tests");
        report.recommendations.push_back("Strengthen sandbox configuration");
        report.recommendations.push_back("Update security policies");
    }

    return report;
}

SandboxedLuaEngine::SecurityTestResult SandboxedLuaEngine::testSecurityBoundary(const std::string& testType) {
    SecurityTestResult result;
    result.testName = testType;

    auto startTime = std::chrono::high_resolution_clock::now();

    if (testType == "dangerous_functions") {
        result.description = "Test prevention of dangerous function access";

        // Test scripts that try to access dangerous functions
        std::vector<std::string> dangerousScripts = {
            "os.execute('ls')",
            "io.open('/etc/passwd', 'r')",
            "debug.getinfo(1)",
            "package.loadlib('malicious.so', 'init')",
            "require('socket')"
        };

        bool allBlocked = true;
        for (const auto& script : dangerousScripts) {
            ExecutionContext context;
            context.script = script;
            context.timeout = std::chrono::milliseconds(1000);

            auto report = executeScript(context);
            if (report.result == ExecutionResult::Success) {
                allBlocked = false;
                result.violations.push_back("Dangerous function executed: " + script);
            }
        }

        result.passed = allBlocked;

    } else if (testType == "file_access") {
        result.description = "Test prevention of file system access";

        ExecutionContext context;
        context.script = "local f = io.open('/tmp/test.txt', 'w'); if f then f:write('test'); f:close(); end";
        context.allowFileAccess = false;

        auto report = executeScript(context);
        result.passed = (report.result != ExecutionResult::Success);

        if (!result.passed) {
            result.violations.push_back("File access was not blocked");
        }

    } else if (testType == "network_access") {
        result.description = "Test prevention of network access";

        ExecutionContext context;
        context.script = "local socket = require('socket'); local tcp = socket.tcp(); tcp:connect('google.com', 80)";
        context.allowNetworkAccess = false;

        auto report = executeScript(context);
        result.passed = (report.result != ExecutionResult::Success);

        if (!result.passed) {
            result.violations.push_back("Network access was not blocked");
        }

    } else if (testType == "system_calls") {
        result.description = "Test prevention of system calls";

        ExecutionContext context;
        context.script = "os.execute('whoami')";
        context.allowSystemCalls = false;

        auto report = executeScript(context);
        result.passed = (report.result != ExecutionResult::Success);

        if (!result.passed) {
            result.violations.push_back("System call was not blocked");
        }

    } else if (testType == "memory_limits") {
        result.description = "Test memory limit enforcement";

        ExecutionContext context;
        context.script = "local t = {}; for i = 1, 1000000 do t[i] = string.rep('x', 1000) end";
        context.memoryLimit = 1024 * 1024; // 1MB limit

        auto report = executeScript(context);
        result.passed = (report.result == ExecutionResult::ResourceLimitExceeded);

        if (!result.passed) {
            result.violations.push_back("Memory limit was not enforced");
        }

    } else if (testType == "timeout_limits") {
        result.description = "Test timeout enforcement";

        ExecutionContext context;
        context.script = "while true do end"; // Infinite loop
        context.timeout = std::chrono::milliseconds(100);

        auto report = executeScript(context);
        result.passed = (report.result == ExecutionResult::TimeoutError);

        if (!result.passed) {
            result.violations.push_back("Timeout was not enforced");
        }

    } else if (testType == "code_injection") {
        result.description = "Test prevention of code injection";

        ExecutionContext context;
        context.script = "loadstring('os.execute(\"rm -rf /\")')()";

        auto report = executeScript(context);
        result.passed = (report.result != ExecutionResult::Success);

        if (!result.passed) {
            result.violations.push_back("Code injection was not prevented");
        }

    } else {
        result.description = "Unknown test type";
        result.passed = false;
        result.violations.push_back("Invalid test type specified");
    }

    auto endTime = std::chrono::high_resolution_clock::now();
    result.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

    return result;
}

} // namespace LuaRuntime
} // namespace RobloxResearch
