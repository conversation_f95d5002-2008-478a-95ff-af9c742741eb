#pragma once

#include <vector>
#include <string>
#include <memory>
#include <sys/types.h>

namespace RobloxResearch {
namespace ProcessMonitor {

/**
 * @brief Security-compliant process analysis tool for research purposes
 * 
 * This class provides methods to analyze running processes while respecting
 * macOS security boundaries. All operations work within SIP constraints and
 * use only documented, legitimate APIs.
 */
class ProcessAnalyzer {
public:
    struct ProcessInfo {
        pid_t pid;
        std::string name;
        std::string bundleIdentifier;
        bool isRobloxProcess;
        bool hasSecurityRestrictions;
        std::string architecture; // "arm64", "x86_64", or "universal"
    };

    struct SecurityConstraints {
        bool sipEnabled;
        bool isCodeSigned;
        bool isSandboxed;
        bool hasEntitlements;
        std::vector<std::string> availableAPIs;
    };

    ProcessAnalyzer();
    ~ProcessAnalyzer();

    /**
     * @brief Get list of running processes (using legitimate APIs only)
     * @return Vector of ProcessInfo structures
     */
    std::vector<ProcessInfo> getRunningProcesses() const;

    /**
     * @brief Find Roblox-related processes
     * @return Vector of Roblox ProcessInfo structures
     */
    std::vector<ProcessInfo> findRobloxProcesses() const;

    /**
     * @brief Analyze security constraints for a given process
     * @param pid Process ID to analyze
     * @return SecurityConstraints structure with analysis results
     */
    SecurityConstraints analyzeSecurityConstraints(pid_t pid) const;

    /**
     * @brief Check if process interaction is possible within security boundaries
     * @param pid Target process ID
     * @return true if legitimate interaction is possible, false otherwise
     */
    bool canInteractWithProcess(pid_t pid) const;

    /**
     * @brief Get available inter-process communication methods
     * @param pid Target process ID
     * @return Vector of available IPC method names
     */
    std::vector<std::string> getAvailableIPCMethods(pid_t pid) const;

    /**
     * @brief Validate that our research tool respects security boundaries
     * @return true if all security checks pass
     */
    bool validateSecurityCompliance() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Security validation methods
    bool checkSIPStatus() const;
    bool validateCodeSigning(pid_t pid) const;
    bool checkSandboxStatus(pid_t pid) const;
    std::vector<std::string> getProcessEntitlements(pid_t pid) const;
};

/**
 * @brief Security boundary checker for research validation
 */
class SecurityBoundaryChecker {
public:
    /**
     * @brief Verify that all operations respect macOS security model
     * @return Detailed security compliance report
     */
    static std::string generateSecurityComplianceReport();

    /**
     * @brief Check if SIP is properly enabled
     * @return true if SIP is enabled and functioning
     */
    static bool verifySIPEnabled();

    /**
     * @brief Validate that no security bypasses are attempted
     * @return true if all operations are legitimate
     */
    static bool validateNoSecurityBypasses();
};

} // namespace ProcessMonitor
} // namespace RobloxResearch
