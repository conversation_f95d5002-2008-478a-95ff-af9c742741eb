#pragma once

#include <vector>
#include <string>
#include <sys/types.h>

namespace RobloxResearch {
namespace ProcessMonitor {

/**
 * @brief Security boundary checker for process analysis
 */
class SecurityBoundaryChecker {
public:
    struct SecurityBoundary {
        pid_t processId;
        bool sipProtected;
        bool sandboxed;
        bool codeSigningValid;
        bool memoryProtected;
    };

    SecurityBoundaryChecker();
    ~SecurityBoundaryChecker();

    /**
     * @brief Validate System Integrity Protection status
     * @return true if SIP is enabled
     */
    bool validateSIPStatus();

    /**
     * @brief Analyze security boundaries for a process
     * @param pid Process ID to analyze
     * @return Security boundary information
     */
    SecurityBoundary analyzeProcessBoundaries(pid_t pid);

    /**
     * @brief Get security violations for a process
     * @param pid Process ID to check
     * @return Vector of security violations
     */
    std::vector<std::string> getSecurityViolations(pid_t pid);

    /**
     * @brief Check if a process is secure
     * @param pid Process ID to check
     * @return true if process is secure
     */
    bool isProcessSecure(pid_t pid);
};

} // namespace ProcessMonitor
} // namespace RobloxResearch
