#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <fstream>
#include <mutex>

namespace RobloxResearch {
namespace Logging {

/**
 * @brief Comprehensive security event logging system
 * 
 * This class provides detailed logging capabilities for security research,
 * including event tracking, audit trails, and automated report generation.
 */
class SecurityLogger {
public:
    enum class LogLevel {
        DEBUG,
        INFO,
        WARNING,
        ERROR,
        CRITICAL,
        SECURITY_EVENT
    };

    enum class EventType {
        SCRIPT_EXECUTION,
        PROCESS_ANALYSIS,
        MEMORY_ANALYSIS,
        SECURITY_VIOLATION,
        COMPLIANCE_CHECK,
        FRAMEWORK_INIT,
        USER_ACTION,
        SYSTEM_EVENT
    };

    struct LogEntry {
        std::chrono::system_clock::time_point timestamp;
        LogLevel level;
        EventType eventType;
        std::string component;
        std::string message;
        std::string details;
        std::string userId;
        std::string sessionId;
    };

    struct SecurityEvent {
        std::chrono::system_clock::time_point timestamp;
        std::string eventName;
        std::string description;
        std::string severity;
        std::vector<std::string> affectedComponents;
        std::string mitigationAction;
        bool resolved;
    };

    struct AuditTrail {
        std::string sessionId;
        std::chrono::system_clock::time_point sessionStart;
        std::chrono::system_clock::time_point sessionEnd;
        std::vector<LogEntry> entries;
        std::vector<SecurityEvent> securityEvents;
        std::string summary;
    };

    SecurityLogger();
    ~SecurityLogger();

    /**
     * @brief Get singleton instance
     * @return Reference to the singleton instance
     */
    static SecurityLogger& getInstance();

    /**
     * @brief Initialize logging system
     * @param logDirectory Directory for log files
     * @param sessionId Unique session identifier
     * @return true if initialization successful
     */
    bool initialize(const std::string& logDirectory, const std::string& sessionId);

    /**
     * @brief Log a general message
     * @param level Log level
     * @param component Component generating the log
     * @param message Log message
     * @param details Additional details
     */
    void log(LogLevel level, const std::string& component, 
             const std::string& message, const std::string& details = "");

    /**
     * @brief Log a security event
     * @param eventType Type of security event
     * @param component Component generating the event
     * @param message Event message
     * @param details Event details
     */
    void logSecurityEvent(EventType eventType, const std::string& component,
                         const std::string& message, const std::string& details = "");

    /**
     * @brief Log a critical security violation
     * @param violation Description of the violation
     * @param component Component where violation occurred
     * @param severity Severity level
     * @param mitigationAction Action taken to mitigate
     */
    void logSecurityViolation(const std::string& violation, const std::string& component,
                             const std::string& severity, const std::string& mitigationAction);

    /**
     * @brief Start a new audit session
     * @param sessionId Unique session identifier
     */
    void startAuditSession(const std::string& sessionId);

    /**
     * @brief End current audit session
     * @param summary Session summary
     */
    void endAuditSession(const std::string& summary);

    /**
     * @brief Generate comprehensive security report
     * @return Formatted security report
     */
    std::string generateSecurityReport();

    /**
     * @brief Generate audit trail report
     * @return Formatted audit trail
     */
    std::string generateAuditTrail();

    /**
     * @brief Get recent log entries
     * @param count Number of entries to retrieve
     * @return Vector of recent log entries
     */
    std::vector<LogEntry> getRecentEntries(size_t count = 100);

    /**
     * @brief Get security events for time period
     * @param startTime Start of time period
     * @param endTime End of time period
     * @return Vector of security events
     */
    std::vector<SecurityEvent> getSecurityEvents(
        std::chrono::system_clock::time_point startTime,
        std::chrono::system_clock::time_point endTime);

    /**
     * @brief Export logs to file
     * @param filename Output filename
     * @param format Export format (json, csv, txt)
     * @return true if export successful
     */
    bool exportLogs(const std::string& filename, const std::string& format = "txt");

    /**
     * @brief Set log level filter
     * @param minLevel Minimum log level to record
     */
    void setLogLevel(LogLevel minLevel);

    /**
     * @brief Enable/disable real-time logging
     * @param enabled Whether real-time logging is enabled
     */
    void setRealTimeLogging(bool enabled);

    /**
     * @brief Get logging statistics
     * @return Statistics about logged events
     */
    std::string getLoggingStatistics();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Helper methods
    std::string formatLogEntry(const LogEntry& entry);
    std::string formatSecurityEvent(const SecurityEvent& event);
    std::string logLevelToString(LogLevel level);
    std::string eventTypeToString(EventType type);
    void writeToFile(const std::string& content);
    void rotateLogFiles();
};

/**
 * @brief Automated report generator
 */
class ReportGenerator {
public:
    struct ReportConfiguration {
        bool includeLuaAnalysis;
        bool includeMemoryAnalysis;
        bool includeProcessAnalysis;
        bool includeSecurityEvents;
        bool includeAuditTrail;
        bool includeRecommendations;
        std::string reportFormat; // "html", "pdf", "txt", "json"
        std::string outputPath;
    };

    struct ComprehensiveReport {
        std::string executiveSummary;
        std::string securityAnalysis;
        std::string technicalFindings;
        std::string riskAssessment;
        std::vector<std::string> recommendations;
        std::string auditTrail;
        std::chrono::system_clock::time_point generationTime;
    };

    /**
     * @brief Generate comprehensive security research report
     * @param config Report configuration
     * @return Generated report
     */
    static ComprehensiveReport generateComprehensiveReport(const ReportConfiguration& config);

    /**
     * @brief Generate executive summary
     * @return Executive summary of security research
     */
    static std::string generateExecutiveSummary();

    /**
     * @brief Generate technical findings report
     * @return Detailed technical findings
     */
    static std::string generateTechnicalFindings();

    /**
     * @brief Generate risk assessment
     * @return Security risk assessment
     */
    static std::string generateRiskAssessment();

    /**
     * @brief Generate security recommendations
     * @return Vector of security recommendations
     */
    static std::vector<std::string> generateRecommendations();

    /**
     * @brief Export report to file
     * @param report Report to export
     * @param filename Output filename
     * @param format Export format
     * @return true if export successful
     */
    static bool exportReport(const ComprehensiveReport& report, 
                           const std::string& filename, 
                           const std::string& format);
};

/**
 * @brief Performance metrics and monitoring
 */
class PerformanceMonitor {
public:
    struct PerformanceMetrics {
        std::chrono::milliseconds totalExecutionTime;
        size_t scriptsExecuted;
        size_t processesAnalyzed;
        size_t memoryAnalysisRuns;
        size_t securityViolations;
        double averageScriptExecutionTime;
        double systemResourceUsage;
    };

    /**
     * @brief Start performance monitoring
     */
    static void startMonitoring();

    /**
     * @brief Stop performance monitoring
     */
    static void stopMonitoring();

    /**
     * @brief Record script execution time
     * @param executionTime Time taken to execute script
     */
    static void recordScriptExecution(std::chrono::milliseconds executionTime);

    /**
     * @brief Record process analysis
     */
    static void recordProcessAnalysis();

    /**
     * @brief Record memory analysis
     */
    static void recordMemoryAnalysis();

    /**
     * @brief Record security violation
     */
    static void recordSecurityViolation();

    /**
     * @brief Get current performance metrics
     * @return Current performance metrics
     */
    static PerformanceMetrics getMetrics();

    /**
     * @brief Generate performance report
     * @return Formatted performance report
     */
    static std::string generatePerformanceReport();

    /**
     * @brief Reset performance counters
     */
    static void resetCounters();
};

/**
 * @brief Global logging macros for convenience
 */
#define LOG_DEBUG(component, message, details) \
    SecurityLogger::getInstance().log(SecurityLogger::LogLevel::DEBUG, component, message, details)

#define LOG_INFO(component, message, details) \
    SecurityLogger::getInstance().log(SecurityLogger::LogLevel::INFO, component, message, details)

#define LOG_WARNING(component, message, details) \
    SecurityLogger::getInstance().log(SecurityLogger::LogLevel::WARNING, component, message, details)

#define LOG_ERROR(component, message, details) \
    SecurityLogger::getInstance().log(SecurityLogger::LogLevel::ERROR, component, message, details)

#define LOG_SECURITY_EVENT(eventType, component, message, details) \
    SecurityLogger::getInstance().logSecurityEvent(eventType, component, message, details)

#define LOG_SECURITY_VIOLATION(violation, component, severity, mitigation) \
    SecurityLogger::getInstance().logSecurityViolation(violation, component, severity, mitigation)

} // namespace Logging
} // namespace RobloxResearch
