#include "SecurityLogger.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <thread>

namespace RobloxResearch {
namespace Logging {

// Static instance for singleton pattern
static std::unique_ptr<SecurityLogger> g_instance;
static std::mutex g_instanceMutex;

class SecurityLogger::Impl {
public:
    std::string logDirectory;
    std::string sessionId;
    std::ofstream logFile;
    std::vector<LogEntry> logEntries;
    std::vector<SecurityEvent> securityEvents;
    AuditTrail currentAuditTrail;
    LogLevel minLogLevel;
    bool realTimeLogging;
    std::mutex logMutex;
    
    Impl() : minLogLevel(LogLevel::INFO), realTimeLogging(true) {}
    
    void ensureLogDirectory() {
        if (!std::filesystem::exists(logDirectory)) {
            std::filesystem::create_directories(logDirectory);
        }
    }
    
    std::string getCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
};

SecurityLogger::SecurityLogger() : pImpl(std::make_unique<Impl>()) {}

SecurityLogger::~SecurityLogger() = default;

SecurityLogger& SecurityLogger::getInstance() {
    std::lock_guard<std::mutex> lock(g_instanceMutex);
    if (!g_instance) {
        g_instance = std::make_unique<SecurityLogger>();
    }
    return *g_instance;
}

bool SecurityLogger::initialize(const std::string& logDirectory, const std::string& sessionId) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    pImpl->logDirectory = logDirectory;
    pImpl->sessionId = sessionId;
    
    try {
        pImpl->ensureLogDirectory();
        
        std::string logFilename = logDirectory + "/security_log_" + sessionId + ".txt";
        pImpl->logFile.open(logFilename, std::ios::app);
        
        if (!pImpl->logFile.is_open()) {
            std::cerr << "Failed to open log file: " << logFilename << std::endl;
            return false;
        }
        
        // Log initialization
        log(LogLevel::INFO, "SecurityLogger", "Logging system initialized", 
            "Session ID: " + sessionId + ", Log Directory: " + logDirectory);
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing SecurityLogger: " << e.what() << std::endl;
        return false;
    }
}

void SecurityLogger::log(LogLevel level, const std::string& component, 
                        const std::string& message, const std::string& details) {
    if (level < pImpl->minLogLevel) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    LogEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.level = level;
    entry.eventType = EventType::SYSTEM_EVENT;
    entry.component = component;
    entry.message = message;
    entry.details = details;
    entry.sessionId = pImpl->sessionId;
    
    pImpl->logEntries.push_back(entry);
    pImpl->currentAuditTrail.entries.push_back(entry);
    
    if (pImpl->realTimeLogging && pImpl->logFile.is_open()) {
        pImpl->logFile << formatLogEntry(entry) << std::endl;
        pImpl->logFile.flush();
    }
}

void SecurityLogger::logSecurityEvent(EventType eventType, const std::string& component,
                                     const std::string& message, const std::string& details) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    LogEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.level = LogLevel::SECURITY_EVENT;
    entry.eventType = eventType;
    entry.component = component;
    entry.message = message;
    entry.details = details;
    entry.sessionId = pImpl->sessionId;
    
    pImpl->logEntries.push_back(entry);
    pImpl->currentAuditTrail.entries.push_back(entry);
    
    // Create security event
    SecurityEvent secEvent;
    secEvent.timestamp = entry.timestamp;
    secEvent.eventName = eventTypeToString(eventType);
    secEvent.description = message;
    secEvent.severity = "MEDIUM";
    secEvent.affectedComponents.push_back(component);
    secEvent.resolved = false;
    
    pImpl->securityEvents.push_back(secEvent);
    pImpl->currentAuditTrail.securityEvents.push_back(secEvent);
    
    if (pImpl->realTimeLogging && pImpl->logFile.is_open()) {
        pImpl->logFile << formatLogEntry(entry) << std::endl;
        pImpl->logFile << "SECURITY EVENT: " << formatSecurityEvent(secEvent) << std::endl;
        pImpl->logFile.flush();
    }
}

void SecurityLogger::logSecurityViolation(const std::string& violation, const std::string& component,
                                         const std::string& severity, const std::string& mitigationAction) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    LogEntry entry;
    entry.timestamp = std::chrono::system_clock::now();
    entry.level = LogLevel::CRITICAL;
    entry.eventType = EventType::SECURITY_VIOLATION;
    entry.component = component;
    entry.message = "SECURITY VIOLATION: " + violation;
    entry.details = "Severity: " + severity + ", Mitigation: " + mitigationAction;
    entry.sessionId = pImpl->sessionId;
    
    pImpl->logEntries.push_back(entry);
    pImpl->currentAuditTrail.entries.push_back(entry);
    
    // Create critical security event
    SecurityEvent secEvent;
    secEvent.timestamp = entry.timestamp;
    secEvent.eventName = "SECURITY_VIOLATION";
    secEvent.description = violation;
    secEvent.severity = severity;
    secEvent.affectedComponents.push_back(component);
    secEvent.mitigationAction = mitigationAction;
    secEvent.resolved = !mitigationAction.empty();
    
    pImpl->securityEvents.push_back(secEvent);
    pImpl->currentAuditTrail.securityEvents.push_back(secEvent);
    
    if (pImpl->realTimeLogging && pImpl->logFile.is_open()) {
        pImpl->logFile << "*** CRITICAL SECURITY VIOLATION ***" << std::endl;
        pImpl->logFile << formatLogEntry(entry) << std::endl;
        pImpl->logFile << formatSecurityEvent(secEvent) << std::endl;
        pImpl->logFile << "*** END SECURITY VIOLATION ***" << std::endl;
        pImpl->logFile.flush();
    }
}

void SecurityLogger::startAuditSession(const std::string& sessionId) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    pImpl->currentAuditTrail.sessionId = sessionId;
    pImpl->currentAuditTrail.sessionStart = std::chrono::system_clock::now();
    pImpl->currentAuditTrail.entries.clear();
    pImpl->currentAuditTrail.securityEvents.clear();
    
    log(LogLevel::INFO, "AuditTrail", "Audit session started", "Session ID: " + sessionId);
}

void SecurityLogger::endAuditSession(const std::string& summary) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    pImpl->currentAuditTrail.sessionEnd = std::chrono::system_clock::now();
    pImpl->currentAuditTrail.summary = summary;
    
    log(LogLevel::INFO, "AuditTrail", "Audit session ended", summary);
}

std::string SecurityLogger::generateSecurityReport() {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    std::ostringstream report;
    
    report << "=== SECURITY RESEARCH REPORT ===" << std::endl;
    report << "Generated: " << pImpl->getCurrentTimestamp() << std::endl;
    report << "Session ID: " << pImpl->sessionId << std::endl;
    report << std::endl;
    
    // Summary statistics
    size_t totalEvents = pImpl->logEntries.size();
    size_t securityEvents = 0;
    size_t violations = 0;
    size_t criticalEvents = 0;
    
    for (const auto& entry : pImpl->logEntries) {
        if (entry.level == LogLevel::SECURITY_EVENT) securityEvents++;
        if (entry.eventType == EventType::SECURITY_VIOLATION) violations++;
        if (entry.level == LogLevel::CRITICAL) criticalEvents++;
    }
    
    report << "=== SUMMARY STATISTICS ===" << std::endl;
    report << "Total Log Entries: " << totalEvents << std::endl;
    report << "Security Events: " << securityEvents << std::endl;
    report << "Security Violations: " << violations << std::endl;
    report << "Critical Events: " << criticalEvents << std::endl;
    report << std::endl;
    
    // Recent security events
    report << "=== RECENT SECURITY EVENTS ===" << std::endl;
    auto recentEvents = getSecurityEvents(
        std::chrono::system_clock::now() - std::chrono::hours(24),
        std::chrono::system_clock::now()
    );
    
    for (const auto& event : recentEvents) {
        report << formatSecurityEvent(event) << std::endl;
    }
    
    // Security assessment
    report << std::endl << "=== SECURITY ASSESSMENT ===" << std::endl;
    if (violations == 0) {
        report << "Status: SECURE - No security violations detected" << std::endl;
        report << "Risk Level: LOW" << std::endl;
    } else if (violations < 5) {
        report << "Status: MODERATE RISK - Limited security violations detected" << std::endl;
        report << "Risk Level: MEDIUM" << std::endl;
    } else {
        report << "Status: HIGH RISK - Multiple security violations detected" << std::endl;
        report << "Risk Level: HIGH" << std::endl;
    }
    
    return report.str();
}

std::string SecurityLogger::generateAuditTrail() {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    std::ostringstream trail;
    
    trail << "=== AUDIT TRAIL ===" << std::endl;
    trail << "Session ID: " << pImpl->currentAuditTrail.sessionId << std::endl;
    
    auto startTime = std::chrono::system_clock::to_time_t(pImpl->currentAuditTrail.sessionStart);
    auto endTime = std::chrono::system_clock::to_time_t(pImpl->currentAuditTrail.sessionEnd);
    
    trail << "Session Start: " << std::put_time(std::localtime(&startTime), "%Y-%m-%d %H:%M:%S") << std::endl;
    trail << "Session End: " << std::put_time(std::localtime(&endTime), "%Y-%m-%d %H:%M:%S") << std::endl;
    trail << "Summary: " << pImpl->currentAuditTrail.summary << std::endl;
    trail << std::endl;
    
    trail << "=== CHRONOLOGICAL EVENT LOG ===" << std::endl;
    for (const auto& entry : pImpl->currentAuditTrail.entries) {
        trail << formatLogEntry(entry) << std::endl;
    }
    
    trail << std::endl << "=== SECURITY EVENTS ===" << std::endl;
    for (const auto& event : pImpl->currentAuditTrail.securityEvents) {
        trail << formatSecurityEvent(event) << std::endl;
    }
    
    return trail.str();
}

std::vector<SecurityLogger::LogEntry> SecurityLogger::getRecentEntries(size_t count) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    std::vector<LogEntry> recent;
    size_t startIndex = pImpl->logEntries.size() > count ? pImpl->logEntries.size() - count : 0;
    
    for (size_t i = startIndex; i < pImpl->logEntries.size(); ++i) {
        recent.push_back(pImpl->logEntries[i]);
    }
    
    return recent;
}

std::vector<SecurityLogger::SecurityEvent> SecurityLogger::getSecurityEvents(
    std::chrono::system_clock::time_point startTime,
    std::chrono::system_clock::time_point endTime) {
    
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    std::vector<SecurityEvent> events;
    
    for (const auto& event : pImpl->securityEvents) {
        if (event.timestamp >= startTime && event.timestamp <= endTime) {
            events.push_back(event);
        }
    }
    
    return events;
}

bool SecurityLogger::exportLogs(const std::string& filename, const std::string& format) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    try {
        std::ofstream outFile(filename);
        if (!outFile.is_open()) {
            return false;
        }
        
        if (format == "json") {
            // Export as JSON
            outFile << "{" << std::endl;
            outFile << "  \"session_id\": \"" << pImpl->sessionId << "\"," << std::endl;
            outFile << "  \"log_entries\": [" << std::endl;
            
            for (size_t i = 0; i < pImpl->logEntries.size(); ++i) {
                const auto& entry = pImpl->logEntries[i];
                outFile << "    {" << std::endl;
                outFile << "      \"timestamp\": \"" << pImpl->getCurrentTimestamp() << "\"," << std::endl;
                outFile << "      \"level\": \"" << logLevelToString(entry.level) << "\"," << std::endl;
                outFile << "      \"component\": \"" << entry.component << "\"," << std::endl;
                outFile << "      \"message\": \"" << entry.message << "\"," << std::endl;
                outFile << "      \"details\": \"" << entry.details << "\"" << std::endl;
                outFile << "    }" << (i < pImpl->logEntries.size() - 1 ? "," : "") << std::endl;
            }
            
            outFile << "  ]" << std::endl;
            outFile << "}" << std::endl;
        } else {
            // Export as plain text
            for (const auto& entry : pImpl->logEntries) {
                outFile << formatLogEntry(entry) << std::endl;
            }
        }
        
        outFile.close();
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error exporting logs: " << e.what() << std::endl;
        return false;
    }
}

void SecurityLogger::setLogLevel(LogLevel minLevel) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    pImpl->minLogLevel = minLevel;
}

void SecurityLogger::setRealTimeLogging(bool enabled) {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    pImpl->realTimeLogging = enabled;
}

std::string SecurityLogger::getLoggingStatistics() {
    std::lock_guard<std::mutex> lock(pImpl->logMutex);
    
    std::ostringstream stats;
    
    stats << "=== LOGGING STATISTICS ===" << std::endl;
    stats << "Total Entries: " << pImpl->logEntries.size() << std::endl;
    stats << "Security Events: " << pImpl->securityEvents.size() << std::endl;
    stats << "Session ID: " << pImpl->sessionId << std::endl;
    stats << "Log Directory: " << pImpl->logDirectory << std::endl;
    stats << "Real-time Logging: " << (pImpl->realTimeLogging ? "Enabled" : "Disabled") << std::endl;
    stats << "Min Log Level: " << logLevelToString(pImpl->minLogLevel) << std::endl;
    
    return stats.str();
}

// Helper method implementations
std::string SecurityLogger::formatLogEntry(const LogEntry& entry) {
    std::ostringstream formatted;
    
    auto time_t = std::chrono::system_clock::to_time_t(entry.timestamp);
    formatted << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] ";
    formatted << "[" << logLevelToString(entry.level) << "] ";
    formatted << "[" << entry.component << "] ";
    formatted << entry.message;
    
    if (!entry.details.empty()) {
        formatted << " - " << entry.details;
    }
    
    return formatted.str();
}

std::string SecurityLogger::formatSecurityEvent(const SecurityEvent& event) {
    std::ostringstream formatted;
    
    auto time_t = std::chrono::system_clock::to_time_t(event.timestamp);
    formatted << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "] ";
    formatted << "SECURITY EVENT: " << event.eventName;
    formatted << " | Severity: " << event.severity;
    formatted << " | Description: " << event.description;
    formatted << " | Status: " << (event.resolved ? "RESOLVED" : "ACTIVE");
    
    return formatted.str();
}

std::string SecurityLogger::logLevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        case LogLevel::SECURITY_EVENT: return "SECURITY";
        default: return "UNKNOWN";
    }
}

std::string SecurityLogger::eventTypeToString(EventType type) {
    switch (type) {
        case EventType::SCRIPT_EXECUTION: return "SCRIPT_EXECUTION";
        case EventType::PROCESS_ANALYSIS: return "PROCESS_ANALYSIS";
        case EventType::MEMORY_ANALYSIS: return "MEMORY_ANALYSIS";
        case EventType::SECURITY_VIOLATION: return "SECURITY_VIOLATION";
        case EventType::COMPLIANCE_CHECK: return "COMPLIANCE_CHECK";
        case EventType::FRAMEWORK_INIT: return "FRAMEWORK_INIT";
        case EventType::USER_ACTION: return "USER_ACTION";
        case EventType::SYSTEM_EVENT: return "SYSTEM_EVENT";
        default: return "UNKNOWN";
    }
}

void SecurityLogger::writeToFile(const std::string& content) {
    if (pImpl->logFile.is_open()) {
        pImpl->logFile << content << std::endl;
        pImpl->logFile.flush();
    }
}

void SecurityLogger::rotateLogFiles() {
    // Implementation for log rotation would go here
    // This is a placeholder for future enhancement
}

} // namespace Logging
} // namespace RobloxResearch
