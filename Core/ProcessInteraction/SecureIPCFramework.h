#pragma once

#include <Foundation/Foundation.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>

namespace RobloxResearch {
namespace ProcessInteraction {

/**
 * @brief Secure Inter-Process Communication framework for research
 * 
 * This framework demonstrates legitimate IPC methods that work within
 * macOS security constraints. All methods respect SIP and sandboxing.
 */
class SecureIPCFramework {
public:
    enum class IPCMethod {
        DistributedNotifications,
        CFMessagePort,
        XPCService,
        AppleEvents,
        Pasteboard,
        FileSystemWatcher
    };

    struct IPCMessage {
        std::string sender;
        std::string recipient;
        std::string payload;
        std::chrono::system_clock::time_point timestamp;
        IPCMethod method;
    };

    struct IPCCapabilities {
        std::vector<IPCMethod> availableMethods;
        bool canSendMessages;
        bool canReceiveMessages;
        std::vector<std::string> securityLimitations;
    };

    SecureIPCFramework();
    ~SecureIPCFramework();

    /**
     * @brief Analyze available IPC methods for target process
     * @param targetPID Process ID to analyze
     * @return Capabilities structure with available methods
     */
    IPCCapabilities analyzeIPCCapabilities(pid_t targetPID);

    /**
     * @brief Send message using specified IPC method
     * @param method IPC method to use
     * @param message Message to send
     * @return true if message was sent successfully
     */
    bool sendMessage(IPCMethod method, const IPCMessage& message);

    /**
     * @brief Register message handler for incoming messages
     * @param method IPC method to listen on
     * @param handler Function to call when message received
     * @return true if handler was registered successfully
     */
    bool registerMessageHandler(IPCMethod method, 
                               std::function<void(const IPCMessage&)> handler);

    /**
     * @brief Start listening for messages
     * @return true if listening started successfully
     */
    bool startListening();

    /**
     * @brief Stop listening for messages
     */
    void stopListening();

    /**
     * @brief Get security compliance status
     * @return true if all IPC methods are legitimate and secure
     */
    bool validateSecurityCompliance() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Distributed Notifications IPC implementation
 */
class DistributedNotificationIPC {
public:
    DistributedNotificationIPC();
    ~DistributedNotificationIPC();

    bool sendNotification(const std::string& name, const std::string& payload);
    bool registerObserver(const std::string& name, 
                         std::function<void(const std::string&)> handler);
    void removeObserver(const std::string& name);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief CFMessagePort IPC implementation
 */
class MessagePortIPC {
public:
    MessagePortIPC();
    ~MessagePortIPC();

    bool createLocalPort(const std::string& portName);
    bool sendMessage(const std::string& portName, const std::string& message);
    bool registerPortHandler(std::function<std::string(const std::string&)> handler);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief XPC Service IPC implementation
 */
class XPCServiceIPC {
public:
    XPCServiceIPC();
    ~XPCServiceIPC();

    bool createService(const std::string& serviceName);
    bool sendXPCMessage(const std::string& serviceName, const std::string& message);
    bool registerXPCHandler(std::function<void(const std::string&)> handler);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Apple Events IPC implementation
 */
class AppleEventsIPC {
public:
    AppleEventsIPC();
    ~AppleEventsIPC();

    bool sendAppleEvent(pid_t targetPID, const std::string& eventClass, 
                       const std::string& eventID, const std::string& data);
    bool registerEventHandler(const std::string& eventClass, 
                             std::function<void(const std::string&)> handler);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Pasteboard IPC implementation
 */
class PasteboardIPC {
public:
    PasteboardIPC();
    ~PasteboardIPC();

    bool writeToCustomPasteboard(const std::string& pasteboardName, 
                                const std::string& data);
    std::string readFromCustomPasteboard(const std::string& pasteboardName);
    bool registerPasteboardWatcher(const std::string& pasteboardName,
                                  std::function<void(const std::string&)> handler);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief File System Watcher IPC implementation
 */
class FileSystemWatcherIPC {
public:
    FileSystemWatcherIPC();
    ~FileSystemWatcherIPC();

    bool createWatchedDirectory(const std::string& path);
    bool writeMessage(const std::string& path, const std::string& message);
    bool registerFileWatcher(const std::string& path,
                           std::function<void(const std::string&)> handler);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Security validator for IPC operations
 */
class IPCSecurityValidator {
public:
    /**
     * @brief Validate that IPC method respects security boundaries
     * @param method IPC method to validate
     * @return true if method is secure and legitimate
     */
    static bool validateIPCMethod(SecureIPCFramework::IPCMethod method);

    /**
     * @brief Check if message content is safe
     * @param message Message to validate
     * @return Vector of security concerns found
     */
    static std::vector<std::string> validateMessageSecurity(const std::string& message);

    /**
     * @brief Generate security compliance report for IPC framework
     * @return Detailed security analysis
     */
    static std::string generateIPCSecurityReport();
};

} // namespace ProcessInteraction
} // namespace RobloxResearch
