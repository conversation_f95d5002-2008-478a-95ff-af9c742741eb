#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>
#include <map>

namespace RobloxResearch {
namespace ProcessInteraction {

/**
 * @brief Roblox process injection framework for macOS
 * 
 * This class implements actual dynamic library injection into Roblox processes
 * using various techniques to bypass macOS security measures.
 */
class RobloxInjector {
public:
    enum class InjectionMethod {
        DYLD_INSERT_LIBRARIES,  // Environment variable injection
        DLOPEN_INJECTION,       // Runtime library loading
        MACH_INJECTION,         // Mach port injection
        FRAMEWORK_HIJACKING,    // Framework replacement
        LIBRARY_INTERPOSITION   // Library interposition
    };

    enum class InjectionStatus {
        SUCCESS,
        PROCESS_NOT_FOUND,
        ACCESS_DENIED,
        INJECTION_FAILED,
        DYLIB_NOT_FOUND,
        SIGNATURE_INVALID,
        TIMEOUT
    };

    struct ProcessInfo {
        pid_t pid;
        std::string name;
        std::string bundleId;
        std::string executablePath;
        bool isRoblox;
        bool isAccessible;
        std::vector<std::string> loadedLibraries;
    };

    struct InjectionResult {
        InjectionStatus status;
        std::string dylibPath;
        pid_t targetPid;
        InjectionMethod method;
        std::chrono::system_clock::time_point injectionTime;
        std::string errorMessage;
        bool codeExecutionVerified;
    };

    struct InjectionConfig {
        std::string dylibPath;
        InjectionMethod preferredMethod;
        bool autoDetectRoblox;
        bool verifyInjection;
        std::chrono::seconds timeout{30};
        bool enableLogging;
    };

    RobloxInjector();
    ~RobloxInjector();

    /**
     * @brief Find all running Roblox processes
     * @return Vector of Roblox process information
     */
    std::vector<ProcessInfo> findRobloxProcesses();

    /**
     * @brief Inject dylib into target Roblox process
     * @param targetPid Process ID to inject into
     * @param config Injection configuration
     * @return Injection result with status and details
     */
    InjectionResult injectIntoProcess(pid_t targetPid, const InjectionConfig& config);

    /**
     * @brief Inject into first available Roblox process
     * @param config Injection configuration
     * @return Injection result with status and details
     */
    InjectionResult injectIntoRoblox(const InjectionConfig& config);

    /**
     * @brief Verify that injection was successful
     * @param targetPid Process ID to check
     * @param dylibPath Path to injected dylib
     * @return true if injection is verified
     */
    bool verifyInjection(pid_t targetPid, const std::string& dylibPath);

    /**
     * @brief Execute Lua code in injected process
     * @param targetPid Process ID with injected dylib
     * @param luaCode Lua code to execute
     * @return Execution result
     */
    std::string executeLuaCode(pid_t targetPid, const std::string& luaCode);

    /**
     * @brief Create injection dylib with Lua runtime
     * @param outputPath Where to create the dylib
     * @param includeRobloxAPI Whether to include Roblox API bindings
     * @return true if dylib created successfully
     */
    bool createInjectionDylib(const std::string& outputPath, bool includeRobloxAPI = true);

    /**
     * @brief Sign dylib for injection (bypasses code signing)
     * @param dylibPath Path to dylib to sign
     * @return true if signing successful
     */
    bool signDylibForInjection(const std::string& dylibPath);

    /**
     * @brief Get detailed process information
     * @param pid Process ID to analyze
     * @return Detailed process information
     */
    ProcessInfo getProcessInfo(pid_t pid);

    /**
     * @brief Check if process is injectable
     * @param pid Process ID to check
     * @return true if process can be injected into
     */
    bool isProcessInjectable(pid_t pid);

    /**
     * @brief Monitor injected process for crashes or issues
     * @param targetPid Process ID to monitor
     * @param duration How long to monitor
     * @return Vector of detected issues
     */
    std::vector<std::string> monitorInjectedProcess(pid_t targetPid, std::chrono::seconds duration);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Injection method implementations
    InjectionResult performDyldInjection(pid_t targetPid, const std::string& dylibPath);
    InjectionResult performDlopenInjection(pid_t targetPid, const std::string& dylibPath);
    InjectionResult performMachInjection(pid_t targetPid, const std::string& dylibPath);
    InjectionResult performFrameworkHijacking(pid_t targetPid, const std::string& dylibPath);
    InjectionResult performLibraryInterposition(pid_t targetPid, const std::string& dylibPath);

    // Helper methods
    bool disableSIPForProcess(pid_t targetPid);
    bool bypassCodeSigning(const std::string& dylibPath);
    bool setupInjectionEnvironment();
    std::string generateDylibSource(bool includeRobloxAPI);
    bool compileDylib(const std::string& sourceCode, const std::string& outputPath);
    std::vector<pid_t> getProcessesByName(const std::string& processName);
    bool isRobloxProcess(pid_t pid);
    std::string getProcessBundleId(pid_t pid);
    std::vector<std::string> getLoadedLibraries(pid_t pid);
    bool hasTaskForPidAccess();
    bool requestTaskForPidAccess();
};

} // namespace ProcessInteraction
} // namespace RobloxResearch
