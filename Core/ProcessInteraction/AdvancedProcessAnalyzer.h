#pragma once

#include "../ProcessMonitor/ProcessAnalyzer.h"
#include <Security/Security.h>
#include <CoreFoundation/CoreFoundation.h>
#include <vector>
#include <string>
#include <memory>
#include <chrono>
#include <functional>

namespace RobloxResearch {
namespace ProcessInteraction {

/**
 * @brief Advanced process interaction analysis for security research
 * 
 * This class provides comprehensive analysis of process interaction capabilities,
 * focusing on the differences between signed and unsigned applications and
 * the security implications of code signing removal.
 */
class AdvancedProcessAnalyzer {
public:
    enum class CodeSigningStatus {
        ValidSigned,
        InvalidSigned,
        Unsigned,
        SignatureRemoved,
        Unknown
    };

    enum class SecurityLevel {
        Maximum,    // Fully signed, SIP enabled, all protections active
        High,       // Signed but some protections bypassed
        Medium,     // Unsigned but SIP active
        Low,        // Unsigned, some protections disabled
        Minimal     // Most protections bypassed (research only)
    };

    struct ProcessSecurityProfile {
        pid_t pid;
        std::string processName;
        std::string bundlePath;
        CodeSigningStatus signingStatus;
        SecurityLevel securityLevel;
        
        // Code signing details
        std::string signingIdentity;
        std::vector<std::string> entitlements;
        bool hasHardenedRuntime;
        bool isNotarized;
        
        // Security capabilities
        bool canAccessOtherProcesses;
        bool canInjectCode;
        bool canModifyMemory;
        bool canBypassSandbox;
        
        // Protection mechanisms
        bool sipProtected;
        bool gatekeeperValidated;
        bool quarantineApplied;
        std::vector<std::string> activeProtections;
        
        // Analysis metadata
        std::chrono::system_clock::time_point analysisTime;
        std::string analysisNotes;
    };

    struct InteractionCapabilities {
        std::vector<std::string> availableIPCMethods;
        std::vector<std::string> blockedMethods;
        std::vector<std::string> securityLimitations;
        bool canEstablishConnection;
        bool requiresElevation;
        std::string recommendedApproach;
    };

    struct SecurityComparison {
        ProcessSecurityProfile signedProfile;
        ProcessSecurityProfile unsignedProfile;
        std::vector<std::string> securityDifferences;
        std::vector<std::string> newVulnerabilities;
        std::vector<std::string> lostProtections;
        std::string riskAssessment;
    };

    AdvancedProcessAnalyzer();
    ~AdvancedProcessAnalyzer();

    /**
     * @brief Analyze comprehensive security profile of a process
     * @param pid Process ID to analyze
     * @return Detailed security profile
     */
    ProcessSecurityProfile analyzeProcessSecurity(pid_t pid);

    /**
     * @brief Compare security profiles of signed vs unsigned versions
     * @param signedPath Path to signed application
     * @param unsignedPath Path to unsigned application
     * @return Security comparison analysis
     */
    SecurityComparison compareSignedVsUnsigned(const std::string& signedPath, 
                                               const std::string& unsignedPath);

    /**
     * @brief Analyze interaction capabilities between processes
     * @param sourcePid Source process PID
     * @param targetPid Target process PID
     * @return Interaction capabilities analysis
     */
    InteractionCapabilities analyzeInteractionCapabilities(pid_t sourcePid, pid_t targetPid);

    /**
     * @brief Detect code signing removal effects
     * @param applicationPath Path to application to analyze
     * @return Analysis of code signing removal impact
     */
    std::vector<std::string> analyzeCodeSigningRemovalEffects(const std::string& applicationPath);

    /**
     * @brief Monitor process security changes in real-time
     * @param pid Process to monitor
     * @param duration How long to monitor
     * @return Vector of security events detected
     */
    std::vector<std::string> monitorSecurityChanges(pid_t pid, std::chrono::seconds duration);

    /**
     * @brief Generate comprehensive security research report
     * @param processes Vector of processes to include in report
     * @return Detailed security analysis report
     */
    std::string generateSecurityResearchReport(const std::vector<pid_t>& processes) const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Code signing analysis methods
    CodeSigningStatus analyzeCodeSigning(const std::string& path);
    std::vector<std::string> extractEntitlements(const std::string& path);
    bool checkHardenedRuntime(const std::string& path);
    bool checkNotarization(const std::string& path);
    
    // Security level assessment
    SecurityLevel assessSecurityLevel(const ProcessSecurityProfile& profile);
    std::vector<std::string> identifyActiveProtections(pid_t pid);
    
    // Interaction analysis
    bool canProcessInteract(pid_t source, pid_t target);
    std::vector<std::string> getBlockedInteractionMethods(pid_t source, pid_t target);
};

/**
 * @brief Roblox-specific security analysis
 */
class RobloxSecurityAnalyzer {
public:
    struct RobloxSecurityState {
        bool isRobloxRunning;
        std::vector<pid_t> robloxProcesses;
        bool hasValidSignature;
        bool isQuarantined;
        SecurityLevel currentSecurityLevel;
        std::vector<std::string> vulnerabilities;
        std::vector<std::string> protections;
    };

    /**
     * @brief Analyze current Roblox security state
     * @return Comprehensive Roblox security analysis
     */
    static RobloxSecurityState analyzeRobloxSecurity();

    /**
     * @brief Simulate effects of the provided downloader script
     * @return Analysis of security implications
     */
    static std::vector<std::string> analyzeDownloaderScriptEffects();

    /**
     * @brief Compare Roblox security before and after signature removal
     * @return Security comparison analysis
     */
    static SecurityComparison compareRobloxSignatureRemoval();

    /**
     * @brief Generate Roblox-specific security recommendations
     * @return Vector of security recommendations
     */
    static std::vector<std::string> generateSecurityRecommendations();
};

/**
 * @brief Security event monitoring and logging
 */
class SecurityEventMonitor {
public:
    enum class EventType {
        ProcessLaunch,
        ProcessTermination,
        CodeSigningViolation,
        SandboxViolation,
        MemoryAccess,
        IPCAttempt,
        SecurityBypass,
        ProtectionDisabled
    };

    struct SecurityEvent {
        EventType type;
        std::chrono::system_clock::time_point timestamp;
        pid_t sourcePid;
        pid_t targetPid;
        std::string description;
        std::string severity;
        std::vector<std::string> details;
    };

    SecurityEventMonitor();
    ~SecurityEventMonitor();

    /**
     * @brief Start monitoring security events
     * @return true if monitoring started successfully
     */
    bool startMonitoring();

    /**
     * @brief Stop monitoring security events
     */
    void stopMonitoring();

    /**
     * @brief Get collected security events
     * @return Vector of security events
     */
    std::vector<SecurityEvent> getSecurityEvents() const;

    /**
     * @brief Register callback for real-time event notification
     * @param callback Function to call when event occurs
     */
    void registerEventCallback(std::function<void(const SecurityEvent&)> callback);

    /**
     * @brief Generate security event report
     * @return Formatted security event report
     */
    std::string generateEventReport() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace ProcessInteraction
} // namespace RobloxResearch
