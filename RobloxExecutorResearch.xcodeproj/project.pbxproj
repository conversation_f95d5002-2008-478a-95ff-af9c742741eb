// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001000000000000001 /* ProcessAnalyzer.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A1000002000000000000001 /* ProcessAnalyzer.cpp */; };
		A1000003000000000000001 /* SandboxedLuaEngine.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A1000004000000000000001 /* SandboxedLuaEngine.cpp */; };
		A1000005000000000000001 /* main.cpp in Sources */ = {isa = PBXBuildFile; fileRef = A1000006000000000000001 /* main.cpp */; };
		A1000007000000000000001 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1000008000000000000001 /* Foundation.framework */; };
		A1000009000000000000001 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1000010000000000000001 /* Security.framework */; };
		A1000011000000000000001 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1000012000000000000001 /* Cocoa.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1000002000000000000001 /* ProcessAnalyzer.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = ProcessAnalyzer.cpp; sourceTree = "<group>"; };
		A1000004000000000000001 /* SandboxedLuaEngine.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = SandboxedLuaEngine.cpp; sourceTree = "<group>"; };
		A1000006000000000000001 /* main.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = main.cpp; sourceTree = "<group>"; };
		A1000008000000000000001 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		A1000010000000000000001 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		A1000012000000000000001 /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		A1000013000000000000001 /* RobloxExecutorResearch */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = RobloxExecutorResearch; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000014000000000000001 /* ProcessAnalyzer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ProcessAnalyzer.h; sourceTree = "<group>"; };
		A1000015000000000000001 /* SandboxedLuaEngine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SandboxedLuaEngine.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1000016000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000007000000000000001 /* Foundation.framework in Frameworks */,
				A1000009000000000000001 /* Security.framework in Frameworks */,
				A1000011000000000000001 /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1000017000000000000001 = {
			isa = PBXGroup;
			children = (
				A1000018000000000000001 /* Core */,
				A1000019000000000000001 /* UI */,
				A1000020000000000000001 /* Tests */,
				A1000021000000000000001 /* Products */,
				A1000022000000000000001 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A1000018000000000000001 /* Core */ = {
			isa = PBXGroup;
			children = (
				A1000023000000000000001 /* ProcessMonitor */,
				A1000024000000000000001 /* LuaRuntime */,
				A1000025000000000000001 /* ProcessInteraction */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		A1000019000000000000001 /* UI */ = {
			isa = PBXGroup;
			children = (
			);
			path = UI;
			sourceTree = "<group>";
		};
		A1000020000000000000001 /* Tests */ = {
			isa = PBXGroup;
			children = (
				A1000006000000000000001 /* main.cpp */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		A1000021000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000013000000000000001 /* RobloxExecutorResearch */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000022000000000000001 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A1000008000000000000001 /* Foundation.framework */,
				A1000010000000000000001 /* Security.framework */,
				A1000012000000000000001 /* Cocoa.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A1000023000000000000001 /* ProcessMonitor */ = {
			isa = PBXGroup;
			children = (
				A1000014000000000000001 /* ProcessAnalyzer.h */,
				A1000002000000000000001 /* ProcessAnalyzer.cpp */,
			);
			path = ProcessMonitor;
			sourceTree = "<group>";
		};
		A1000024000000000000001 /* LuaRuntime */ = {
			isa = PBXGroup;
			children = (
				A1000015000000000000001 /* SandboxedLuaEngine.h */,
				A1000004000000000000001 /* SandboxedLuaEngine.cpp */,
			);
			path = LuaRuntime;
			sourceTree = "<group>";
		};
		A1000025000000000000001 /* ProcessInteraction */ = {
			isa = PBXGroup;
			children = (
			);
			path = ProcessInteraction;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1000026000000000000001 /* RobloxExecutorResearch */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000027000000000000001 /* Build configuration list for PBXNativeTarget "RobloxExecutorResearch" */;
			buildPhases = (
				A1000028000000000000001 /* Sources */,
				A1000016000000000000001 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RobloxExecutorResearch;
			productName = RobloxExecutorResearch;
			productReference = A1000013000000000000001 /* RobloxExecutorResearch */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000029000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1000026000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1000030000000000000001 /* Build configuration list for PBXProject "RobloxExecutorResearch" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1000017000000000000001;
			productRefGroup = A1000021000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1000026000000000000001 /* RobloxExecutorResearch */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		A1000028000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000001000000000000001 /* ProcessAnalyzer.cpp in Sources */,
				A1000003000000000000001 /* SandboxedLuaEngine.cpp in Sources */,
				A1000005000000000000001 /* main.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1000031000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		A1000032000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1000027000000000000001 /* Build configuration list for PBXNativeTarget "RobloxExecutorResearch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000033000000000000001 /* Debug */,
				A1000034000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000030000000000000001 /* Build configuration list for PBXProject "RobloxExecutorResearch" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000031000000000000001 /* Debug */,
				A1000032000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1000029000000000000001 /* Project object */;
}
