#!/bin/bash

# Roblox Executor Security Research Framework
# Dependency Installation Script for macOS

set -e  # Exit on any error

echo "=== Roblox Executor Security Research Framework ==="
echo "Installing dependencies for macOS security research..."
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS only"
    exit 1
fi

# Check macOS version
macos_version=$(sw_vers -productVersion)
print_status "Detected macOS version: $macos_version"

# Check if macOS version is supported (11.0+)
if [[ $(echo "$macos_version" | cut -d. -f1) -lt 11 ]]; then
    print_error "macOS 11.0 (Big Sur) or later is required"
    exit 1
fi

# CRITICAL: Check System Integrity Protection status
print_status "Checking System Integrity Protection (SIP) status..."
sip_status=$(csrutil status)
if [[ $sip_status == *"enabled"* ]]; then
    print_success "SIP is enabled - research environment is valid"
else
    print_error "SIP is DISABLED - this compromises research validity"
    print_error "Please enable SIP with: csrutil enable (requires reboot)"
    print_error "Research should only be conducted with SIP enabled"
    exit 1
fi

# Check if Xcode Command Line Tools are installed
print_status "Checking for Xcode Command Line Tools..."
if ! xcode-select -p &> /dev/null; then
    print_warning "Xcode Command Line Tools not found"
    print_status "Installing Xcode Command Line Tools..."
    xcode-select --install
    print_status "Please complete the installation and run this script again"
    exit 1
else
    print_success "Xcode Command Line Tools found"
fi

# Check if Homebrew is installed
print_status "Checking for Homebrew..."
if ! command -v brew &> /dev/null; then
    print_warning "Homebrew not found"
    print_status "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
else
    print_success "Homebrew found"
    print_status "Updating Homebrew..."
    brew update
fi

# Install required packages
print_status "Installing required packages..."

# Install LuaJIT
print_status "Installing LuaJIT..."
if brew list luajit &> /dev/null; then
    print_success "LuaJIT already installed"
else
    brew install luajit
    print_success "LuaJIT installed"
fi

# Install CMake
print_status "Installing CMake..."
if brew list cmake &> /dev/null; then
    print_success "CMake already installed"
else
    brew install cmake
    print_success "CMake installed"
fi

# Install pkg-config
print_status "Installing pkg-config..."
if brew list pkg-config &> /dev/null; then
    print_success "pkg-config already installed"
else
    brew install pkg-config
    print_success "pkg-config installed"
fi

# Verify installations
print_status "Verifying installations..."

# Check LuaJIT
if command -v luajit &> /dev/null; then
    luajit_version=$(luajit -v 2>&1 | head -n1)
    print_success "LuaJIT verified: $luajit_version"
else
    print_error "LuaJIT installation failed"
    exit 1
fi

# Check CMake
if command -v cmake &> /dev/null; then
    cmake_version=$(cmake --version | head -n1)
    print_success "CMake verified: $cmake_version"
else
    print_error "CMake installation failed"
    exit 1
fi

# Check pkg-config
if command -v pkg-config &> /dev/null; then
    pkgconfig_version=$(pkg-config --version)
    print_success "pkg-config verified: version $pkgconfig_version"
else
    print_error "pkg-config installation failed"
    exit 1
fi

# Check code signing tools
print_status "Verifying code signing tools..."
if command -v codesign &> /dev/null; then
    codesign_version=$(codesign --version 2>&1 | head -n1)
    print_success "Code signing tools verified: $codesign_version"
else
    print_error "Code signing tools not available"
    exit 1
fi

# Set up environment variables
print_status "Setting up environment variables..."

# Detect LuaJIT paths
if [[ $(uname -m) == "arm64" ]]; then
    # Apple Silicon paths
    LUAJIT_PREFIX="/opt/homebrew"
else
    # Intel paths
    LUAJIT_PREFIX="/usr/local"
fi

LUAJIT_INCLUDE_DIR="$LUAJIT_PREFIX/include/luajit-2.1"
LUAJIT_LIBRARY="$LUAJIT_PREFIX/lib/libluajit-5.1.dylib"

# Verify LuaJIT paths exist
if [[ -d "$LUAJIT_INCLUDE_DIR" && -f "$LUAJIT_LIBRARY" ]]; then
    print_success "LuaJIT paths verified"
    echo "LUAJIT_INCLUDE_DIR=$LUAJIT_INCLUDE_DIR"
    echo "LUAJIT_LIBRARY=$LUAJIT_LIBRARY"
else
    print_error "LuaJIT paths not found"
    print_error "Include dir: $LUAJIT_INCLUDE_DIR"
    print_error "Library: $LUAJIT_LIBRARY"
    exit 1
fi

# Create build environment script
print_status "Creating build environment script..."
cat > build_env.sh << EOF
#!/bin/bash
# Build environment for Roblox Executor Security Research Framework

export LUAJIT_INCLUDE_DIR="$LUAJIT_INCLUDE_DIR"
export LUAJIT_LIBRARY="$LUAJIT_LIBRARY"
export CMAKE_PREFIX_PATH="$LUAJIT_PREFIX"

echo "Build environment configured:"
echo "  LuaJIT Include: \$LUAJIT_INCLUDE_DIR"
echo "  LuaJIT Library: \$LUAJIT_LIBRARY"
echo "  CMake Prefix: \$CMAKE_PREFIX_PATH"
EOF

chmod +x build_env.sh
print_success "Build environment script created: build_env.sh"

# Security validation
print_status "Performing security validation..."

# Check Gatekeeper status
gatekeeper_status=$(spctl --status 2>&1)
if [[ $gatekeeper_status == *"enabled"* ]]; then
    print_success "Gatekeeper is enabled"
else
    print_warning "Gatekeeper may be disabled"
fi

# Check for security tools
if command -v spctl &> /dev/null; then
    print_success "Security policy tools available"
else
    print_warning "Security policy tools not found"
fi

# Final summary
echo ""
echo "=== Installation Summary ==="
print_success "All dependencies installed successfully"
print_success "System security validation passed"
print_success "Build environment configured"

echo ""
echo "Next steps:"
echo "1. Source the build environment: source build_env.sh"
echo "2. Create build directory: mkdir build && cd build"
echo "3. Configure build: cmake .."
echo "4. Build project: make -j\$(sysctl -n hw.ncpu)"
echo "5. Run tests: ./ResearchTests"

echo ""
print_warning "IMPORTANT SECURITY NOTES:"
echo "- SIP must remain ENABLED for valid research"
echo "- This framework demonstrates security limitations, not bypasses"
echo "- All operations work within macOS security constraints"
echo "- Research findings document why traditional executors fail"

echo ""
print_success "Installation complete!"
