# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/ComprehensiveTestSuite.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ComprehensiveTestSuite.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ComprehensiveTestSuite.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ComprehensiveTestSuite.dir/flags.make

CMakeFiles/ComprehensiveTestSuite.dir/codegen:
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/codegen

CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o: CMakeFiles/ComprehensiveTestSuite.dir/flags.make
CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveTestSuite.cpp
CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o: CMakeFiles/ComprehensiveTestSuite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o -MF CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o.d -o CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveTestSuite.cpp

CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveTestSuite.cpp > CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.i

CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveTestSuite.cpp -o CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.s

CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o: CMakeFiles/ComprehensiveTestSuite.dir/flags.make
CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/test_main.cpp
CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o: CMakeFiles/ComprehensiveTestSuite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o -MF CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o.d -o CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/test_main.cpp

CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/test_main.cpp > CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.i

CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/test_main.cpp -o CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.s

# Object files for target ComprehensiveTestSuite
ComprehensiveTestSuite_OBJECTS = \
"CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o" \
"CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o"

# External object files for target ComprehensiveTestSuite
ComprehensiveTestSuite_EXTERNAL_OBJECTS =

ComprehensiveTestSuite: CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o
ComprehensiveTestSuite: CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o
ComprehensiveTestSuite: CMakeFiles/ComprehensiveTestSuite.dir/build.make
ComprehensiveTestSuite: libRobloxResearchCore.a
ComprehensiveTestSuite: /opt/homebrew/lib/libluajit-5.1.dylib
ComprehensiveTestSuite: CMakeFiles/ComprehensiveTestSuite.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable ComprehensiveTestSuite"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ComprehensiveTestSuite.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ComprehensiveTestSuite.dir/build: ComprehensiveTestSuite
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/build

CMakeFiles/ComprehensiveTestSuite.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ComprehensiveTestSuite.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/clean

CMakeFiles/ComprehensiveTestSuite.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/ComprehensiveTestSuite.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/depend

