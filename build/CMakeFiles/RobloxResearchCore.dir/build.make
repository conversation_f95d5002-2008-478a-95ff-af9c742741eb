# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/RobloxResearchCore.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/RobloxResearchCore.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/RobloxResearchCore.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/RobloxResearchCore.dir/flags.make

CMakeFiles/RobloxResearchCore.dir/codegen:
.PHONY : CMakeFiles/RobloxResearchCore.dir/codegen

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/ProcessAnalyzer.cpp
CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/ProcessAnalyzer.cpp

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/ProcessAnalyzer.cpp > CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/ProcessAnalyzer.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/SecurityBoundaryChecker.cpp
CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/SecurityBoundaryChecker.cpp

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/SecurityBoundaryChecker.cpp > CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/SecurityBoundaryChecker.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SandboxedLuaEngine.cpp
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SandboxedLuaEngine.cpp

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SandboxedLuaEngine.cpp > CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SandboxedLuaEngine.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SecurityWrapper.cpp
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SecurityWrapper.cpp

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SecurityWrapper.cpp > CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SecurityWrapper.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/RobloxAPISimulator.cpp
CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/RobloxAPISimulator.cpp

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/RobloxAPISimulator.cpp > CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/RobloxAPISimulator.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/MemoryAnalyzer.cpp
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/MemoryAnalyzer.cpp

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/MemoryAnalyzer.cpp > CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/MemoryAnalyzer.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/SIPCompliantTools.cpp
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/SIPCompliantTools.cpp

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/SIPCompliantTools.cpp > CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/SIPCompliantTools.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp > CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp
CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp > CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/EducationalDylibFramework.cpp
CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/EducationalDylibFramework.cpp

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/EducationalDylibFramework.cpp > CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/EducationalDylibFramework.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/RobloxInjector.cpp
CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/RobloxInjector.cpp

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/RobloxInjector.cpp > CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/RobloxInjector.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/Integration/SecureIntegrationFramework.cpp
CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/Integration/SecureIntegrationFramework.cpp

CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/Integration/SecureIntegrationFramework.cpp > CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/Integration/SecureIntegrationFramework.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/Logging/SecurityLogger.cpp
CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/Logging/SecurityLogger.cpp

CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/Logging/SecurityLogger.cpp > CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/Logging/SecurityLogger.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.s

CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o: CMakeFiles/RobloxResearchCore.dir/flags.make
CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Core/Compliance/SecurityComplianceValidator.cpp
CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o: CMakeFiles/RobloxResearchCore.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o -MF CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o.d -o CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Core/Compliance/SecurityComplianceValidator.cpp

CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Core/Compliance/SecurityComplianceValidator.cpp > CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.i

CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Core/Compliance/SecurityComplianceValidator.cpp -o CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.s

# Object files for target RobloxResearchCore
RobloxResearchCore_OBJECTS = \
"CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o" \
"CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o"

# External object files for target RobloxResearchCore
RobloxResearchCore_EXTERNAL_OBJECTS =

libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/build.make
libRobloxResearchCore.a: CMakeFiles/RobloxResearchCore.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Linking CXX static library libRobloxResearchCore.a"
	$(CMAKE_COMMAND) -P CMakeFiles/RobloxResearchCore.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/RobloxResearchCore.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/RobloxResearchCore.dir/build: libRobloxResearchCore.a
.PHONY : CMakeFiles/RobloxResearchCore.dir/build

CMakeFiles/RobloxResearchCore.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/RobloxResearchCore.dir/cmake_clean.cmake
.PHONY : CMakeFiles/RobloxResearchCore.dir/clean

CMakeFiles/RobloxResearchCore.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/RobloxResearchCore.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/RobloxResearchCore.dir/depend

