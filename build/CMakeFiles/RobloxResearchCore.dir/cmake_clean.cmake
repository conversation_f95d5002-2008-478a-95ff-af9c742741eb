file(REMOVE_RECURSE
  "CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/RobloxInjector.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o.d"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o"
  "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o.d"
  "libRobloxResearchCore.a"
  "libRobloxResearchCore.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/RobloxResearchCore.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
