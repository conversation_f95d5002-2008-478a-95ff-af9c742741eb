/usr/bin/ar qc libRobloxResearchCore.a CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o
/usr/bin/ranlib libRobloxResearchCore.a
