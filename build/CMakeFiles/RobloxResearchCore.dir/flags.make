# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = 

CXX_INCLUDES = -I/Users/<USER>/Documents/RobloxExecutor/Core -I/opt/homebrew/include/luajit-2.1

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -Wall -Wextra -Werror -fstack-protector-strong -D_FORTIFY_SOURCE=2 -fPIE

CXX_FLAGS = -std=gnu++17 -arch arm64 -mmacosx-version-min=11.0 -Wall -Wextra -Werror -fstack-protector-strong -D_FORTIFY_SOURCE=2 -fPIE

