
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/RobloxExecutor/Core/Compliance/SecurityComplianceValidator.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/Integration/SecureIntegrationFramework.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/Logging/SecurityLogger.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/RobloxAPISimulator.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SandboxedLuaEngine.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/LuaRuntime/SecurityWrapper.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/ProcessInteraction/EducationalDylibFramework.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/ProcessAnalyzer.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/ProcessMonitor/SecurityBoundaryChecker.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/MemoryAnalyzer.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Core/SecurityAnalysis/SIPCompliantTools.cpp" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o" "gcc" "CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
