# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Utility rule file for security_check.

# Include any custom commands dependencies for this target.
include CMakeFiles/security_check.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/security_check.dir/progress.make

CMakeFiles/security_check: ResearchTests
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Validating code signing and security compliance"
	codesign --verify --deep --strict /Users/<USER>/Documents/RobloxExecutor/build/ResearchTests

CMakeFiles/security_check.dir/codegen:
.PHONY : CMakeFiles/security_check.dir/codegen

security_check: CMakeFiles/security_check
security_check: CMakeFiles/security_check.dir/build.make
.PHONY : security_check

# Rule to build all files generated by this target.
CMakeFiles/security_check.dir/build: security_check
.PHONY : CMakeFiles/security_check.dir/build

CMakeFiles/security_check.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/security_check.dir/cmake_clean.cmake
.PHONY : CMakeFiles/security_check.dir/clean

CMakeFiles/security_check.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/security_check.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/security_check.dir/depend

