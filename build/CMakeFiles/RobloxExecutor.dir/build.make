# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/RobloxExecutor.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/RobloxExecutor.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/RobloxExecutor.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/RobloxExecutor.dir/flags.make

CMakeFiles/RobloxExecutor.dir/codegen:
.PHONY : CMakeFiles/RobloxExecutor.dir/codegen

CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o: CMakeFiles/RobloxExecutor.dir/flags.make
CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o: /Users/<USER>/Documents/RobloxExecutor/RobloxExecutor.cpp
CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o: CMakeFiles/RobloxExecutor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o -MF CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o.d -o CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/RobloxExecutor.cpp

CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/RobloxExecutor.cpp > CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.i

CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/RobloxExecutor.cpp -o CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.s

# Object files for target RobloxExecutor
RobloxExecutor_OBJECTS = \
"CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o"

# External object files for target RobloxExecutor
RobloxExecutor_EXTERNAL_OBJECTS =

RobloxExecutor: CMakeFiles/RobloxExecutor.dir/RobloxExecutor.cpp.o
RobloxExecutor: CMakeFiles/RobloxExecutor.dir/build.make
RobloxExecutor: libRobloxResearchCore.a
RobloxExecutor: /opt/homebrew/lib/libluajit-5.1.dylib
RobloxExecutor: CMakeFiles/RobloxExecutor.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable RobloxExecutor"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/RobloxExecutor.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/RobloxExecutor.dir/build: RobloxExecutor
.PHONY : CMakeFiles/RobloxExecutor.dir/build

CMakeFiles/RobloxExecutor.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/RobloxExecutor.dir/cmake_clean.cmake
.PHONY : CMakeFiles/RobloxExecutor.dir/clean

CMakeFiles/RobloxExecutor.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/RobloxExecutor.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/RobloxExecutor.dir/depend

