# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/ComprehensiveTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ComprehensiveTest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ComprehensiveTest.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ComprehensiveTest.dir/flags.make

CMakeFiles/ComprehensiveTest.dir/codegen:
.PHONY : CMakeFiles/ComprehensiveTest.dir/codegen

CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o: CMakeFiles/ComprehensiveTest.dir/flags.make
CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveFrameworkTest.cpp
CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o: CMakeFiles/ComprehensiveTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o -MF CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o.d -o CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveFrameworkTest.cpp

CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveFrameworkTest.cpp > CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.i

CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/ComprehensiveFrameworkTest.cpp -o CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.s

# Object files for target ComprehensiveTest
ComprehensiveTest_OBJECTS = \
"CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o"

# External object files for target ComprehensiveTest
ComprehensiveTest_EXTERNAL_OBJECTS =

ComprehensiveTest: CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o
ComprehensiveTest: CMakeFiles/ComprehensiveTest.dir/build.make
ComprehensiveTest: libRobloxResearchCore.a
ComprehensiveTest: /opt/homebrew/lib/libluajit-5.1.dylib
ComprehensiveTest: CMakeFiles/ComprehensiveTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable ComprehensiveTest"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ComprehensiveTest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ComprehensiveTest.dir/build: ComprehensiveTest
.PHONY : CMakeFiles/ComprehensiveTest.dir/build

CMakeFiles/ComprehensiveTest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ComprehensiveTest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ComprehensiveTest.dir/clean

CMakeFiles/ComprehensiveTest.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/ComprehensiveTest.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ComprehensiveTest.dir/depend

