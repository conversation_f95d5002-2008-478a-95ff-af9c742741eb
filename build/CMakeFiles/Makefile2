# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/RobloxResearchCore.dir/all
all: CMakeFiles/ResearchTests.dir/all
all: CMakeFiles/ComprehensiveTestSuite.dir/all
all: CMakeFiles/ComprehensiveTest.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/RobloxResearchCore.dir/codegen
codegen: CMakeFiles/ResearchTests.dir/codegen
codegen: CMakeFiles/ComprehensiveTestSuite.dir/codegen
codegen: CMakeFiles/ComprehensiveTest.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/RobloxResearchCore.dir/clean
clean: CMakeFiles/ResearchTests.dir/clean
clean: CMakeFiles/ComprehensiveTestSuite.dir/clean
clean: CMakeFiles/ComprehensiveTest.dir/clean
clean: CMakeFiles/security_check.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/RobloxResearchCore.dir

# All Build rule for target.
CMakeFiles/RobloxResearchCore.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19,20,21,22,23,24 "Built target RobloxResearchCore"
.PHONY : CMakeFiles/RobloxResearchCore.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/RobloxResearchCore.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 14
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/RobloxResearchCore.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : CMakeFiles/RobloxResearchCore.dir/rule

# Convenience name for target.
RobloxResearchCore: CMakeFiles/RobloxResearchCore.dir/rule
.PHONY : RobloxResearchCore

# codegen rule for target.
CMakeFiles/RobloxResearchCore.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=11,12,13,14,15,16,17,18,19,20,21,22,23,24 "Finished codegen for target RobloxResearchCore"
.PHONY : CMakeFiles/RobloxResearchCore.dir/codegen

# clean rule for target.
CMakeFiles/RobloxResearchCore.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/clean
.PHONY : CMakeFiles/RobloxResearchCore.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ResearchTests.dir

# All Build rule for target.
CMakeFiles/ResearchTests.dir/all: CMakeFiles/RobloxResearchCore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=6,7,8,9,10 "Built target ResearchTests"
.PHONY : CMakeFiles/ResearchTests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ResearchTests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 19
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ResearchTests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : CMakeFiles/ResearchTests.dir/rule

# Convenience name for target.
ResearchTests: CMakeFiles/ResearchTests.dir/rule
.PHONY : ResearchTests

# codegen rule for target.
CMakeFiles/ResearchTests.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=6,7,8,9,10 "Finished codegen for target ResearchTests"
.PHONY : CMakeFiles/ResearchTests.dir/codegen

# clean rule for target.
CMakeFiles/ResearchTests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/clean
.PHONY : CMakeFiles/ResearchTests.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ComprehensiveTestSuite.dir

# All Build rule for target.
CMakeFiles/ComprehensiveTestSuite.dir/all: CMakeFiles/RobloxResearchCore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=3,4,5 "Built target ComprehensiveTestSuite"
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ComprehensiveTestSuite.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 17
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ComprehensiveTestSuite.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/rule

# Convenience name for target.
ComprehensiveTestSuite: CMakeFiles/ComprehensiveTestSuite.dir/rule
.PHONY : ComprehensiveTestSuite

# codegen rule for target.
CMakeFiles/ComprehensiveTestSuite.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=3,4,5 "Finished codegen for target ComprehensiveTestSuite"
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/codegen

# clean rule for target.
CMakeFiles/ComprehensiveTestSuite.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/clean
.PHONY : CMakeFiles/ComprehensiveTestSuite.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ComprehensiveTest.dir

# All Build rule for target.
CMakeFiles/ComprehensiveTest.dir/all: CMakeFiles/RobloxResearchCore.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=1,2 "Built target ComprehensiveTest"
.PHONY : CMakeFiles/ComprehensiveTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ComprehensiveTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 16
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/ComprehensiveTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : CMakeFiles/ComprehensiveTest.dir/rule

# Convenience name for target.
ComprehensiveTest: CMakeFiles/ComprehensiveTest.dir/rule
.PHONY : ComprehensiveTest

# codegen rule for target.
CMakeFiles/ComprehensiveTest.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=1,2 "Finished codegen for target ComprehensiveTest"
.PHONY : CMakeFiles/ComprehensiveTest.dir/codegen

# clean rule for target.
CMakeFiles/ComprehensiveTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/clean
.PHONY : CMakeFiles/ComprehensiveTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/security_check.dir

# All Build rule for target.
CMakeFiles/security_check.dir/all: CMakeFiles/ResearchTests.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security_check.dir/build.make CMakeFiles/security_check.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security_check.dir/build.make CMakeFiles/security_check.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=25 "Built target security_check"
.PHONY : CMakeFiles/security_check.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/security_check.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/security_check.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : CMakeFiles/security_check.dir/rule

# Convenience name for target.
security_check: CMakeFiles/security_check.dir/rule
.PHONY : security_check

# codegen rule for target.
CMakeFiles/security_check.dir/codegen: CMakeFiles/ResearchTests.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security_check.dir/build.make CMakeFiles/security_check.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=25 "Finished codegen for target security_check"
.PHONY : CMakeFiles/security_check.dir/codegen

# clean rule for target.
CMakeFiles/security_check.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security_check.dir/build.make CMakeFiles/security_check.dir/clean
.PHONY : CMakeFiles/security_check.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

