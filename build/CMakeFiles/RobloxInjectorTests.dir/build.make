# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/RobloxInjectorTests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/RobloxInjectorTests.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/RobloxInjectorTests.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/RobloxInjectorTests.dir/flags.make

CMakeFiles/RobloxInjectorTests.dir/codegen:
.PHONY : CMakeFiles/RobloxInjectorTests.dir/codegen

CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o: CMakeFiles/RobloxInjectorTests.dir/flags.make
CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/RobloxInjectorTests.cpp
CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o: CMakeFiles/RobloxInjectorTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o -MF CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o.d -o CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/RobloxInjectorTests.cpp

CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/RobloxInjectorTests.cpp > CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.i

CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/RobloxInjectorTests.cpp -o CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.s

# Object files for target RobloxInjectorTests
RobloxInjectorTests_OBJECTS = \
"CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o"

# External object files for target RobloxInjectorTests
RobloxInjectorTests_EXTERNAL_OBJECTS =

RobloxInjectorTests: CMakeFiles/RobloxInjectorTests.dir/Tests/RobloxInjectorTests.cpp.o
RobloxInjectorTests: CMakeFiles/RobloxInjectorTests.dir/build.make
RobloxInjectorTests: libRobloxResearchCore.a
RobloxInjectorTests: /opt/homebrew/lib/libluajit-5.1.dylib
RobloxInjectorTests: CMakeFiles/RobloxInjectorTests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable RobloxInjectorTests"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/RobloxInjectorTests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/RobloxInjectorTests.dir/build: RobloxInjectorTests
.PHONY : CMakeFiles/RobloxInjectorTests.dir/build

CMakeFiles/RobloxInjectorTests.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/RobloxInjectorTests.dir/cmake_clean.cmake
.PHONY : CMakeFiles/RobloxInjectorTests.dir/clean

CMakeFiles/RobloxInjectorTests.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/RobloxInjectorTests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/RobloxInjectorTests.dir/depend

