# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

# Include any dependencies generated for this target.
include CMakeFiles/ResearchTests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/ResearchTests.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/ResearchTests.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ResearchTests.dir/flags.make

CMakeFiles/ResearchTests.dir/codegen:
.PHONY : CMakeFiles/ResearchTests.dir/codegen

CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o: CMakeFiles/ResearchTests.dir/flags.make
CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/ProcessMonitorTests.cpp
CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o: CMakeFiles/ResearchTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o -MF CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o.d -o CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/ProcessMonitorTests.cpp

CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/ProcessMonitorTests.cpp > CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.i

CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/ProcessMonitorTests.cpp -o CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.s

CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o: CMakeFiles/ResearchTests.dir/flags.make
CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/LuaRuntimeTests.cpp
CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o: CMakeFiles/ResearchTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o -MF CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o.d -o CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/LuaRuntimeTests.cpp

CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/LuaRuntimeTests.cpp > CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.i

CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/LuaRuntimeTests.cpp -o CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.s

CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o: CMakeFiles/ResearchTests.dir/flags.make
CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/SecurityValidationTests.cpp
CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o: CMakeFiles/ResearchTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o -MF CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o.d -o CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/SecurityValidationTests.cpp

CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/SecurityValidationTests.cpp > CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.i

CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/SecurityValidationTests.cpp -o CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.s

CMakeFiles/ResearchTests.dir/Tests/main.cpp.o: CMakeFiles/ResearchTests.dir/flags.make
CMakeFiles/ResearchTests.dir/Tests/main.cpp.o: /Users/<USER>/Documents/RobloxExecutor/Tests/main.cpp
CMakeFiles/ResearchTests.dir/Tests/main.cpp.o: CMakeFiles/ResearchTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/ResearchTests.dir/Tests/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/ResearchTests.dir/Tests/main.cpp.o -MF CMakeFiles/ResearchTests.dir/Tests/main.cpp.o.d -o CMakeFiles/ResearchTests.dir/Tests/main.cpp.o -c /Users/<USER>/Documents/RobloxExecutor/Tests/main.cpp

CMakeFiles/ResearchTests.dir/Tests/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/ResearchTests.dir/Tests/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/RobloxExecutor/Tests/main.cpp > CMakeFiles/ResearchTests.dir/Tests/main.cpp.i

CMakeFiles/ResearchTests.dir/Tests/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/ResearchTests.dir/Tests/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/RobloxExecutor/Tests/main.cpp -o CMakeFiles/ResearchTests.dir/Tests/main.cpp.s

# Object files for target ResearchTests
ResearchTests_OBJECTS = \
"CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o" \
"CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o" \
"CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o" \
"CMakeFiles/ResearchTests.dir/Tests/main.cpp.o"

# External object files for target ResearchTests
ResearchTests_EXTERNAL_OBJECTS =

ResearchTests: CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o
ResearchTests: CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o
ResearchTests: CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o
ResearchTests: CMakeFiles/ResearchTests.dir/Tests/main.cpp.o
ResearchTests: CMakeFiles/ResearchTests.dir/build.make
ResearchTests: libRobloxResearchCore.a
ResearchTests: /opt/homebrew/lib/libluajit-5.1.dylib
ResearchTests: CMakeFiles/ResearchTests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable ResearchTests"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ResearchTests.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ResearchTests.dir/build: ResearchTests
.PHONY : CMakeFiles/ResearchTests.dir/build

CMakeFiles/ResearchTests.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ResearchTests.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ResearchTests.dir/clean

CMakeFiles/ResearchTests.dir/depend:
	cd /Users/<USER>/Documents/RobloxExecutor/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles/ResearchTests.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/ResearchTests.dir/depend

