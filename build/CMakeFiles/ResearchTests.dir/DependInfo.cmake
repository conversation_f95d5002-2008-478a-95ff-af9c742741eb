
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/Documents/RobloxExecutor/Tests/LuaRuntimeTests.cpp" "CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o" "gcc" "CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Tests/ProcessMonitorTests.cpp" "CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o" "gcc" "CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Tests/SecurityValidationTests.cpp" "CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o" "gcc" "CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o.d"
  "/Users/<USER>/Documents/RobloxExecutor/Tests/main.cpp" "CMakeFiles/ResearchTests.dir/Tests/main.cpp.o" "gcc" "CMakeFiles/ResearchTests.dir/Tests/main.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
