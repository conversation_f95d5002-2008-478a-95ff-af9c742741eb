/usr/bin/c++  -arch arm64 -mmacosx-version-min=11.0 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o CMakeFiles/ResearchTests.dir/Tests/main.cpp.o -o ResearchTests  libRobloxResearchCore.a /opt/homebrew/lib/libluajit-5.1.dylib -framework Foundation -framework Security -framework CoreFoundation
