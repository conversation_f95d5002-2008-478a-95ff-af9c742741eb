#include <iostream>
#include <unistd.h>
#include <thread>
#include <chrono>

int main() {
    std::cout << "[TARGET] Demo process started (PID: " << getpid() << ")" << std::endl;
    std::cout << "[TARGET] This process will be injected with Lua runtime..." << std::endl;
    
    for (int i = 0; i < 8; i++) {
        std::cout << "[TARGET] Running iteration " << i << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    std::cout << "[TARGET] Demo process completed" << std::endl;
    return 0;
}
