# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/RobloxExecutor

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/RobloxExecutor/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles /Users/<USER>/Documents/RobloxExecutor/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Documents/RobloxExecutor/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named RobloxResearchCore

# Build rule for target.
RobloxResearchCore: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 RobloxResearchCore
.PHONY : RobloxResearchCore

# fast build rule for target.
RobloxResearchCore/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/build
.PHONY : RobloxResearchCore/fast

#=============================================================================
# Target rules for targets named ResearchTests

# Build rule for target.
ResearchTests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ResearchTests
.PHONY : ResearchTests

# fast build rule for target.
ResearchTests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/build
.PHONY : ResearchTests/fast

#=============================================================================
# Target rules for targets named ComprehensiveTestSuite

# Build rule for target.
ComprehensiveTestSuite: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ComprehensiveTestSuite
.PHONY : ComprehensiveTestSuite

# fast build rule for target.
ComprehensiveTestSuite/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/build
.PHONY : ComprehensiveTestSuite/fast

#=============================================================================
# Target rules for targets named ComprehensiveTest

# Build rule for target.
ComprehensiveTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 ComprehensiveTest
.PHONY : ComprehensiveTest

# fast build rule for target.
ComprehensiveTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/build
.PHONY : ComprehensiveTest/fast

#=============================================================================
# Target rules for targets named security_check

# Build rule for target.
security_check: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 security_check
.PHONY : security_check

# fast build rule for target.
security_check/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/security_check.dir/build.make CMakeFiles/security_check.dir/build
.PHONY : security_check/fast

Core/Compliance/SecurityComplianceValidator.o: Core/Compliance/SecurityComplianceValidator.cpp.o
.PHONY : Core/Compliance/SecurityComplianceValidator.o

# target to build an object file
Core/Compliance/SecurityComplianceValidator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.o
.PHONY : Core/Compliance/SecurityComplianceValidator.cpp.o

Core/Compliance/SecurityComplianceValidator.i: Core/Compliance/SecurityComplianceValidator.cpp.i
.PHONY : Core/Compliance/SecurityComplianceValidator.i

# target to preprocess a source file
Core/Compliance/SecurityComplianceValidator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.i
.PHONY : Core/Compliance/SecurityComplianceValidator.cpp.i

Core/Compliance/SecurityComplianceValidator.s: Core/Compliance/SecurityComplianceValidator.cpp.s
.PHONY : Core/Compliance/SecurityComplianceValidator.s

# target to generate assembly for a file
Core/Compliance/SecurityComplianceValidator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Compliance/SecurityComplianceValidator.cpp.s
.PHONY : Core/Compliance/SecurityComplianceValidator.cpp.s

Core/Integration/SecureIntegrationFramework.o: Core/Integration/SecureIntegrationFramework.cpp.o
.PHONY : Core/Integration/SecureIntegrationFramework.o

# target to build an object file
Core/Integration/SecureIntegrationFramework.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.o
.PHONY : Core/Integration/SecureIntegrationFramework.cpp.o

Core/Integration/SecureIntegrationFramework.i: Core/Integration/SecureIntegrationFramework.cpp.i
.PHONY : Core/Integration/SecureIntegrationFramework.i

# target to preprocess a source file
Core/Integration/SecureIntegrationFramework.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.i
.PHONY : Core/Integration/SecureIntegrationFramework.cpp.i

Core/Integration/SecureIntegrationFramework.s: Core/Integration/SecureIntegrationFramework.cpp.s
.PHONY : Core/Integration/SecureIntegrationFramework.s

# target to generate assembly for a file
Core/Integration/SecureIntegrationFramework.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Integration/SecureIntegrationFramework.cpp.s
.PHONY : Core/Integration/SecureIntegrationFramework.cpp.s

Core/Logging/SecurityLogger.o: Core/Logging/SecurityLogger.cpp.o
.PHONY : Core/Logging/SecurityLogger.o

# target to build an object file
Core/Logging/SecurityLogger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.o
.PHONY : Core/Logging/SecurityLogger.cpp.o

Core/Logging/SecurityLogger.i: Core/Logging/SecurityLogger.cpp.i
.PHONY : Core/Logging/SecurityLogger.i

# target to preprocess a source file
Core/Logging/SecurityLogger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.i
.PHONY : Core/Logging/SecurityLogger.cpp.i

Core/Logging/SecurityLogger.s: Core/Logging/SecurityLogger.cpp.s
.PHONY : Core/Logging/SecurityLogger.s

# target to generate assembly for a file
Core/Logging/SecurityLogger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/Logging/SecurityLogger.cpp.s
.PHONY : Core/Logging/SecurityLogger.cpp.s

Core/LuaRuntime/RobloxAPISimulator.o: Core/LuaRuntime/RobloxAPISimulator.cpp.o
.PHONY : Core/LuaRuntime/RobloxAPISimulator.o

# target to build an object file
Core/LuaRuntime/RobloxAPISimulator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.o
.PHONY : Core/LuaRuntime/RobloxAPISimulator.cpp.o

Core/LuaRuntime/RobloxAPISimulator.i: Core/LuaRuntime/RobloxAPISimulator.cpp.i
.PHONY : Core/LuaRuntime/RobloxAPISimulator.i

# target to preprocess a source file
Core/LuaRuntime/RobloxAPISimulator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.i
.PHONY : Core/LuaRuntime/RobloxAPISimulator.cpp.i

Core/LuaRuntime/RobloxAPISimulator.s: Core/LuaRuntime/RobloxAPISimulator.cpp.s
.PHONY : Core/LuaRuntime/RobloxAPISimulator.s

# target to generate assembly for a file
Core/LuaRuntime/RobloxAPISimulator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/RobloxAPISimulator.cpp.s
.PHONY : Core/LuaRuntime/RobloxAPISimulator.cpp.s

Core/LuaRuntime/SandboxedLuaEngine.o: Core/LuaRuntime/SandboxedLuaEngine.cpp.o
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.o

# target to build an object file
Core/LuaRuntime/SandboxedLuaEngine.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.o
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.cpp.o

Core/LuaRuntime/SandboxedLuaEngine.i: Core/LuaRuntime/SandboxedLuaEngine.cpp.i
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.i

# target to preprocess a source file
Core/LuaRuntime/SandboxedLuaEngine.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.i
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.cpp.i

Core/LuaRuntime/SandboxedLuaEngine.s: Core/LuaRuntime/SandboxedLuaEngine.cpp.s
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.s

# target to generate assembly for a file
Core/LuaRuntime/SandboxedLuaEngine.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SandboxedLuaEngine.cpp.s
.PHONY : Core/LuaRuntime/SandboxedLuaEngine.cpp.s

Core/LuaRuntime/SecurityWrapper.o: Core/LuaRuntime/SecurityWrapper.cpp.o
.PHONY : Core/LuaRuntime/SecurityWrapper.o

# target to build an object file
Core/LuaRuntime/SecurityWrapper.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.o
.PHONY : Core/LuaRuntime/SecurityWrapper.cpp.o

Core/LuaRuntime/SecurityWrapper.i: Core/LuaRuntime/SecurityWrapper.cpp.i
.PHONY : Core/LuaRuntime/SecurityWrapper.i

# target to preprocess a source file
Core/LuaRuntime/SecurityWrapper.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.i
.PHONY : Core/LuaRuntime/SecurityWrapper.cpp.i

Core/LuaRuntime/SecurityWrapper.s: Core/LuaRuntime/SecurityWrapper.cpp.s
.PHONY : Core/LuaRuntime/SecurityWrapper.s

# target to generate assembly for a file
Core/LuaRuntime/SecurityWrapper.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/LuaRuntime/SecurityWrapper.cpp.s
.PHONY : Core/LuaRuntime/SecurityWrapper.cpp.s

Core/ProcessInteraction/EducationalDylibFramework.o: Core/ProcessInteraction/EducationalDylibFramework.cpp.o
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.o

# target to build an object file
Core/ProcessInteraction/EducationalDylibFramework.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.o
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.cpp.o

Core/ProcessInteraction/EducationalDylibFramework.i: Core/ProcessInteraction/EducationalDylibFramework.cpp.i
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.i

# target to preprocess a source file
Core/ProcessInteraction/EducationalDylibFramework.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.i
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.cpp.i

Core/ProcessInteraction/EducationalDylibFramework.s: Core/ProcessInteraction/EducationalDylibFramework.cpp.s
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.s

# target to generate assembly for a file
Core/ProcessInteraction/EducationalDylibFramework.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessInteraction/EducationalDylibFramework.cpp.s
.PHONY : Core/ProcessInteraction/EducationalDylibFramework.cpp.s

Core/ProcessMonitor/ProcessAnalyzer.o: Core/ProcessMonitor/ProcessAnalyzer.cpp.o
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.o

# target to build an object file
Core/ProcessMonitor/ProcessAnalyzer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.o
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.cpp.o

Core/ProcessMonitor/ProcessAnalyzer.i: Core/ProcessMonitor/ProcessAnalyzer.cpp.i
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.i

# target to preprocess a source file
Core/ProcessMonitor/ProcessAnalyzer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.i
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.cpp.i

Core/ProcessMonitor/ProcessAnalyzer.s: Core/ProcessMonitor/ProcessAnalyzer.cpp.s
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.s

# target to generate assembly for a file
Core/ProcessMonitor/ProcessAnalyzer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/ProcessAnalyzer.cpp.s
.PHONY : Core/ProcessMonitor/ProcessAnalyzer.cpp.s

Core/ProcessMonitor/SecurityBoundaryChecker.o: Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.o

# target to build an object file
Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.cpp.o

Core/ProcessMonitor/SecurityBoundaryChecker.i: Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.i

# target to preprocess a source file
Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.cpp.i

Core/ProcessMonitor/SecurityBoundaryChecker.s: Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.s

# target to generate assembly for a file
Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s
.PHONY : Core/ProcessMonitor/SecurityBoundaryChecker.cpp.s

Core/SecurityAnalysis/CodeSigningAnalyzer.o: Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.o

# target to build an object file
Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.o

Core/SecurityAnalysis/CodeSigningAnalyzer.i: Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.i

# target to preprocess a source file
Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.i

Core/SecurityAnalysis/CodeSigningAnalyzer.s: Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.s

# target to generate assembly for a file
Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/CodeSigningAnalyzer.cpp.s

Core/SecurityAnalysis/MemoryAnalyzer.o: Core/SecurityAnalysis/MemoryAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.o

# target to build an object file
Core/SecurityAnalysis/MemoryAnalyzer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.cpp.o

Core/SecurityAnalysis/MemoryAnalyzer.i: Core/SecurityAnalysis/MemoryAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.i

# target to preprocess a source file
Core/SecurityAnalysis/MemoryAnalyzer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.cpp.i

Core/SecurityAnalysis/MemoryAnalyzer.s: Core/SecurityAnalysis/MemoryAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.s

# target to generate assembly for a file
Core/SecurityAnalysis/MemoryAnalyzer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/MemoryAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/MemoryAnalyzer.cpp.s

Core/SecurityAnalysis/RobloxApplicationAnalyzer.o: Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.o

# target to build an object file
Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.o

Core/SecurityAnalysis/RobloxApplicationAnalyzer.i: Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.i

# target to preprocess a source file
Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.i

Core/SecurityAnalysis/RobloxApplicationAnalyzer.s: Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.s

# target to generate assembly for a file
Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s
.PHONY : Core/SecurityAnalysis/RobloxApplicationAnalyzer.cpp.s

Core/SecurityAnalysis/SIPCompliantTools.o: Core/SecurityAnalysis/SIPCompliantTools.cpp.o
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.o

# target to build an object file
Core/SecurityAnalysis/SIPCompliantTools.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.o
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.cpp.o

Core/SecurityAnalysis/SIPCompliantTools.i: Core/SecurityAnalysis/SIPCompliantTools.cpp.i
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.i

# target to preprocess a source file
Core/SecurityAnalysis/SIPCompliantTools.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.i
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.cpp.i

Core/SecurityAnalysis/SIPCompliantTools.s: Core/SecurityAnalysis/SIPCompliantTools.cpp.s
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.s

# target to generate assembly for a file
Core/SecurityAnalysis/SIPCompliantTools.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RobloxResearchCore.dir/build.make CMakeFiles/RobloxResearchCore.dir/Core/SecurityAnalysis/SIPCompliantTools.cpp.s
.PHONY : Core/SecurityAnalysis/SIPCompliantTools.cpp.s

Tests/ComprehensiveFrameworkTest.o: Tests/ComprehensiveFrameworkTest.cpp.o
.PHONY : Tests/ComprehensiveFrameworkTest.o

# target to build an object file
Tests/ComprehensiveFrameworkTest.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.o
.PHONY : Tests/ComprehensiveFrameworkTest.cpp.o

Tests/ComprehensiveFrameworkTest.i: Tests/ComprehensiveFrameworkTest.cpp.i
.PHONY : Tests/ComprehensiveFrameworkTest.i

# target to preprocess a source file
Tests/ComprehensiveFrameworkTest.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.i
.PHONY : Tests/ComprehensiveFrameworkTest.cpp.i

Tests/ComprehensiveFrameworkTest.s: Tests/ComprehensiveFrameworkTest.cpp.s
.PHONY : Tests/ComprehensiveFrameworkTest.s

# target to generate assembly for a file
Tests/ComprehensiveFrameworkTest.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTest.dir/build.make CMakeFiles/ComprehensiveTest.dir/Tests/ComprehensiveFrameworkTest.cpp.s
.PHONY : Tests/ComprehensiveFrameworkTest.cpp.s

Tests/ComprehensiveTestSuite.o: Tests/ComprehensiveTestSuite.cpp.o
.PHONY : Tests/ComprehensiveTestSuite.o

# target to build an object file
Tests/ComprehensiveTestSuite.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.o
.PHONY : Tests/ComprehensiveTestSuite.cpp.o

Tests/ComprehensiveTestSuite.i: Tests/ComprehensiveTestSuite.cpp.i
.PHONY : Tests/ComprehensiveTestSuite.i

# target to preprocess a source file
Tests/ComprehensiveTestSuite.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.i
.PHONY : Tests/ComprehensiveTestSuite.cpp.i

Tests/ComprehensiveTestSuite.s: Tests/ComprehensiveTestSuite.cpp.s
.PHONY : Tests/ComprehensiveTestSuite.s

# target to generate assembly for a file
Tests/ComprehensiveTestSuite.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/ComprehensiveTestSuite.cpp.s
.PHONY : Tests/ComprehensiveTestSuite.cpp.s

Tests/LuaRuntimeTests.o: Tests/LuaRuntimeTests.cpp.o
.PHONY : Tests/LuaRuntimeTests.o

# target to build an object file
Tests/LuaRuntimeTests.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.o
.PHONY : Tests/LuaRuntimeTests.cpp.o

Tests/LuaRuntimeTests.i: Tests/LuaRuntimeTests.cpp.i
.PHONY : Tests/LuaRuntimeTests.i

# target to preprocess a source file
Tests/LuaRuntimeTests.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.i
.PHONY : Tests/LuaRuntimeTests.cpp.i

Tests/LuaRuntimeTests.s: Tests/LuaRuntimeTests.cpp.s
.PHONY : Tests/LuaRuntimeTests.s

# target to generate assembly for a file
Tests/LuaRuntimeTests.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/LuaRuntimeTests.cpp.s
.PHONY : Tests/LuaRuntimeTests.cpp.s

Tests/ProcessMonitorTests.o: Tests/ProcessMonitorTests.cpp.o
.PHONY : Tests/ProcessMonitorTests.o

# target to build an object file
Tests/ProcessMonitorTests.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.o
.PHONY : Tests/ProcessMonitorTests.cpp.o

Tests/ProcessMonitorTests.i: Tests/ProcessMonitorTests.cpp.i
.PHONY : Tests/ProcessMonitorTests.i

# target to preprocess a source file
Tests/ProcessMonitorTests.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.i
.PHONY : Tests/ProcessMonitorTests.cpp.i

Tests/ProcessMonitorTests.s: Tests/ProcessMonitorTests.cpp.s
.PHONY : Tests/ProcessMonitorTests.s

# target to generate assembly for a file
Tests/ProcessMonitorTests.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/ProcessMonitorTests.cpp.s
.PHONY : Tests/ProcessMonitorTests.cpp.s

Tests/SecurityValidationTests.o: Tests/SecurityValidationTests.cpp.o
.PHONY : Tests/SecurityValidationTests.o

# target to build an object file
Tests/SecurityValidationTests.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.o
.PHONY : Tests/SecurityValidationTests.cpp.o

Tests/SecurityValidationTests.i: Tests/SecurityValidationTests.cpp.i
.PHONY : Tests/SecurityValidationTests.i

# target to preprocess a source file
Tests/SecurityValidationTests.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.i
.PHONY : Tests/SecurityValidationTests.cpp.i

Tests/SecurityValidationTests.s: Tests/SecurityValidationTests.cpp.s
.PHONY : Tests/SecurityValidationTests.s

# target to generate assembly for a file
Tests/SecurityValidationTests.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/SecurityValidationTests.cpp.s
.PHONY : Tests/SecurityValidationTests.cpp.s

Tests/main.o: Tests/main.cpp.o
.PHONY : Tests/main.o

# target to build an object file
Tests/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/main.cpp.o
.PHONY : Tests/main.cpp.o

Tests/main.i: Tests/main.cpp.i
.PHONY : Tests/main.i

# target to preprocess a source file
Tests/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/main.cpp.i
.PHONY : Tests/main.cpp.i

Tests/main.s: Tests/main.cpp.s
.PHONY : Tests/main.s

# target to generate assembly for a file
Tests/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ResearchTests.dir/build.make CMakeFiles/ResearchTests.dir/Tests/main.cpp.s
.PHONY : Tests/main.cpp.s

Tests/test_main.o: Tests/test_main.cpp.o
.PHONY : Tests/test_main.o

# target to build an object file
Tests/test_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.o
.PHONY : Tests/test_main.cpp.o

Tests/test_main.i: Tests/test_main.cpp.i
.PHONY : Tests/test_main.i

# target to preprocess a source file
Tests/test_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.i
.PHONY : Tests/test_main.cpp.i

Tests/test_main.s: Tests/test_main.cpp.s
.PHONY : Tests/test_main.s

# target to generate assembly for a file
Tests/test_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/ComprehensiveTestSuite.dir/build.make CMakeFiles/ComprehensiveTestSuite.dir/Tests/test_main.cpp.s
.PHONY : Tests/test_main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... security_check"
	@echo "... ComprehensiveTest"
	@echo "... ComprehensiveTestSuite"
	@echo "... ResearchTests"
	@echo "... RobloxResearchCore"
	@echo "... Core/Compliance/SecurityComplianceValidator.o"
	@echo "... Core/Compliance/SecurityComplianceValidator.i"
	@echo "... Core/Compliance/SecurityComplianceValidator.s"
	@echo "... Core/Integration/SecureIntegrationFramework.o"
	@echo "... Core/Integration/SecureIntegrationFramework.i"
	@echo "... Core/Integration/SecureIntegrationFramework.s"
	@echo "... Core/Logging/SecurityLogger.o"
	@echo "... Core/Logging/SecurityLogger.i"
	@echo "... Core/Logging/SecurityLogger.s"
	@echo "... Core/LuaRuntime/RobloxAPISimulator.o"
	@echo "... Core/LuaRuntime/RobloxAPISimulator.i"
	@echo "... Core/LuaRuntime/RobloxAPISimulator.s"
	@echo "... Core/LuaRuntime/SandboxedLuaEngine.o"
	@echo "... Core/LuaRuntime/SandboxedLuaEngine.i"
	@echo "... Core/LuaRuntime/SandboxedLuaEngine.s"
	@echo "... Core/LuaRuntime/SecurityWrapper.o"
	@echo "... Core/LuaRuntime/SecurityWrapper.i"
	@echo "... Core/LuaRuntime/SecurityWrapper.s"
	@echo "... Core/ProcessInteraction/EducationalDylibFramework.o"
	@echo "... Core/ProcessInteraction/EducationalDylibFramework.i"
	@echo "... Core/ProcessInteraction/EducationalDylibFramework.s"
	@echo "... Core/ProcessMonitor/ProcessAnalyzer.o"
	@echo "... Core/ProcessMonitor/ProcessAnalyzer.i"
	@echo "... Core/ProcessMonitor/ProcessAnalyzer.s"
	@echo "... Core/ProcessMonitor/SecurityBoundaryChecker.o"
	@echo "... Core/ProcessMonitor/SecurityBoundaryChecker.i"
	@echo "... Core/ProcessMonitor/SecurityBoundaryChecker.s"
	@echo "... Core/SecurityAnalysis/CodeSigningAnalyzer.o"
	@echo "... Core/SecurityAnalysis/CodeSigningAnalyzer.i"
	@echo "... Core/SecurityAnalysis/CodeSigningAnalyzer.s"
	@echo "... Core/SecurityAnalysis/MemoryAnalyzer.o"
	@echo "... Core/SecurityAnalysis/MemoryAnalyzer.i"
	@echo "... Core/SecurityAnalysis/MemoryAnalyzer.s"
	@echo "... Core/SecurityAnalysis/RobloxApplicationAnalyzer.o"
	@echo "... Core/SecurityAnalysis/RobloxApplicationAnalyzer.i"
	@echo "... Core/SecurityAnalysis/RobloxApplicationAnalyzer.s"
	@echo "... Core/SecurityAnalysis/SIPCompliantTools.o"
	@echo "... Core/SecurityAnalysis/SIPCompliantTools.i"
	@echo "... Core/SecurityAnalysis/SIPCompliantTools.s"
	@echo "... Tests/ComprehensiveFrameworkTest.o"
	@echo "... Tests/ComprehensiveFrameworkTest.i"
	@echo "... Tests/ComprehensiveFrameworkTest.s"
	@echo "... Tests/ComprehensiveTestSuite.o"
	@echo "... Tests/ComprehensiveTestSuite.i"
	@echo "... Tests/ComprehensiveTestSuite.s"
	@echo "... Tests/LuaRuntimeTests.o"
	@echo "... Tests/LuaRuntimeTests.i"
	@echo "... Tests/LuaRuntimeTests.s"
	@echo "... Tests/ProcessMonitorTests.o"
	@echo "... Tests/ProcessMonitorTests.i"
	@echo "... Tests/ProcessMonitorTests.s"
	@echo "... Tests/SecurityValidationTests.o"
	@echo "... Tests/SecurityValidationTests.i"
	@echo "... Tests/SecurityValidationTests.s"
	@echo "... Tests/main.o"
	@echo "... Tests/main.i"
	@echo "... Tests/main.s"
	@echo "... Tests/test_main.o"
	@echo "... Tests/test_main.i"
	@echo "... Tests/test_main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

