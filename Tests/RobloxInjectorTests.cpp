#include "../Core/ProcessInteraction/RobloxInjector.h"
#include "../Core/Logging/SecurityLogger.h"

#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>

using namespace RobloxResearch::ProcessInteraction;
using namespace RobloxResearch::Logging;

class RobloxInjectorTests {
private:
    RobloxInjector injector;
    SecurityLogger logger;
    
public:
    RobloxInjectorTests() {
        logger.log(SecurityLogger::LogLevel::INFO, "RobloxInjectorTests", "Initializing tests");
    }
    
    void testProcessDetection() {
        std::cout << "\n=== Testing Process Detection ===\n";
        
        auto processes = injector.findRobloxProcesses();
        
        std::cout << "Found " << processes.size() << " Roblox process(es)\n";
        
        for (const auto& process : processes) {
            std::cout << "Process: " << process.name << " (PID: " << process.pid << ")\n";
            std::cout << "  Executable: " << process.executablePath << "\n";
            std::cout << "  Bundle ID: " << process.bundleId << "\n";
            std::cout << "  Injectable: " << (process.isAccessible ? "Yes" : "No") << "\n";
            std::cout << "  Libraries: " << process.loadedLibraries.size() << "\n";
        }
        
        if (processes.empty()) {
            std::cout << "⚠ No Roblox processes found. Start Roblox to test injection.\n";
        } else {
            std::cout << "✓ Process detection working\n";
        }
    }
    
    void testDylibCreation() {
        std::cout << "\n=== Testing Dylib Creation ===\n";
        
        std::string testDylibPath = "./test_injection.dylib";
        
        std::cout << "Creating test injection dylib...\n";
        bool success = injector.createInjectionDylib(testDylibPath, true);
        
        if (success) {
            std::cout << "✓ Successfully created dylib: " << testDylibPath << "\n";
            
            // Test signing
            std::cout << "Testing dylib signing...\n";
            if (injector.signDylibForInjection(testDylibPath)) {
                std::cout << "✓ Successfully signed dylib\n";
            } else {
                std::cout << "⚠ Failed to sign dylib\n";
            }
            
            // Verify file exists
            std::ifstream dylibFile(testDylibPath);
            if (dylibFile.good()) {
                std::cout << "✓ Dylib file exists and is readable\n";
            } else {
                std::cout << "✗ Dylib file not found or not readable\n";
            }
            
        } else {
            std::cout << "✗ Failed to create dylib\n";
            std::cout << "Check that you have:\n";
            std::cout << "- Xcode command line tools installed\n";
            std::cout << "- LuaJIT installed (brew install luajit)\n";
        }
    }
    
    void testInjectionSimulation() {
        std::cout << "\n=== Testing Injection Simulation ===\n";
        
        // Create a test dylib first
        std::string testDylibPath = "./test_injection.dylib";
        if (!injector.createInjectionDylib(testDylibPath, true)) {
            std::cout << "✗ Cannot test injection - dylib creation failed\n";
            return;
        }
        
        auto processes = injector.findRobloxProcesses();
        
        if (processes.empty()) {
            std::cout << "⚠ No Roblox processes found for injection testing\n";
            std::cout << "Testing injection configuration...\n";
            
            RobloxInjector::InjectionConfig config;
            config.dylibPath = testDylibPath;
            config.preferredMethod = RobloxInjector::InjectionMethod::DYLD_INSERT_LIBRARIES;
            config.autoDetectRoblox = true;
            config.verifyInjection = true;
            config.enableLogging = true;
            
            auto result = injector.injectIntoRoblox(config);
            
            if (result.status == RobloxInjector::InjectionStatus::PROCESS_NOT_FOUND) {
                std::cout << "✓ Injection correctly detected no Roblox processes\n";
            } else {
                std::cout << "⚠ Unexpected injection result: " << static_cast<int>(result.status) << "\n";
            }
            
        } else {
            std::cout << "Found Roblox processes - testing injection...\n";
            
            auto& targetProcess = processes[0];
            std::cout << "Target: " << targetProcess.name << " (PID: " << targetProcess.pid << ")\n";
            
            if (!targetProcess.isAccessible) {
                std::cout << "⚠ Target process not accessible - may need elevated privileges\n";
            }
            
            RobloxInjector::InjectionConfig config;
            config.dylibPath = testDylibPath;
            config.preferredMethod = RobloxInjector::InjectionMethod::DYLD_INSERT_LIBRARIES;
            config.autoDetectRoblox = true;
            config.verifyInjection = true;
            config.enableLogging = true;
            
            auto result = injector.injectIntoProcess(targetProcess.pid, config);
            
            switch (result.status) {
                case RobloxInjector::InjectionStatus::SUCCESS:
                    {
                        std::cout << "✓ Injection successful!\n";
                        std::cout << "  Target PID: " << result.targetPid << "\n";
                        std::cout << "  Verification: " << (result.codeExecutionVerified ? "Passed" : "Failed") << "\n";

                        // Test Lua execution
                        std::cout << "Testing Lua code execution...\n";
                        std::string testResult = injector.executeLuaCode(result.targetPid, "print('Test from injector')");
                        std::cout << "  Execution result: " << testResult << "\n";

                        break;
                    }

                case RobloxInjector::InjectionStatus::ACCESS_DENIED:
                    std::cout << "⚠ Injection failed - access denied\n";
                    std::cout << "  Try running with elevated privileges\n";
                    break;

                case RobloxInjector::InjectionStatus::INJECTION_FAILED:
                    std::cout << "⚠ Injection failed: " << result.errorMessage << "\n";
                    break;

                default:
                    std::cout << "⚠ Injection failed with status: " << static_cast<int>(result.status) << "\n";
                    break;
            }
        }
    }
    
    void testLuaExecution() {
        std::cout << "\n=== Testing Lua Execution Framework ===\n";
        
        // Test the Lua code generation
        std::cout << "Testing Lua runtime code generation...\n";
        
        std::string testDylibPath = "./lua_test.dylib";
        bool success = injector.createInjectionDylib(testDylibPath, true);
        
        if (success) {
            std::cout << "✓ Lua runtime dylib created successfully\n";
            
            // Verify the dylib contains expected symbols
            std::string nmCmd = "nm \"" + testDylibPath + "\" | grep -E '(execute_lua_code|injection_init)'";
            std::cout << "Checking dylib symbols...\n";
            
            int result = system(nmCmd.c_str());
            if (result == 0) {
                std::cout << "✓ Dylib contains expected symbols\n";
            } else {
                std::cout << "⚠ Could not verify dylib symbols\n";
            }
            
        } else {
            std::cout << "✗ Failed to create Lua runtime dylib\n";
        }
    }
    
    void testProcessMonitoring() {
        std::cout << "\n=== Testing Process Monitoring ===\n";
        
        auto processes = injector.findRobloxProcesses();
        
        if (processes.empty()) {
            std::cout << "⚠ No Roblox processes to monitor\n";
            return;
        }
        
        auto& targetProcess = processes[0];
        std::cout << "Monitoring process: " << targetProcess.name << " (PID: " << targetProcess.pid << ")\n";
        
        // Test short monitoring period
        std::cout << "Running 3-second monitoring test...\n";
        auto issues = injector.monitorInjectedProcess(targetProcess.pid, std::chrono::seconds(3));
        
        if (issues.empty()) {
            std::cout << "✓ No issues detected during monitoring\n";
        } else {
            std::cout << "Issues detected:\n";
            for (const auto& issue : issues) {
                std::cout << "  - " << issue << "\n";
            }
        }
    }
    
    void runAllTests() {
        std::cout << "\n";
        std::cout << "╔══════════════════════════════════════════════════════════════╗\n";
        std::cout << "║                  Roblox Injector Test Suite                 ║\n";
        std::cout << "╚══════════════════════════════════════════════════════════════╝\n";
        
        try {
            testProcessDetection();
            testDylibCreation();
            testLuaExecution();
            testInjectionSimulation();
            testProcessMonitoring();
            
            std::cout << "\n=== Test Summary ===\n";
            std::cout << "All tests completed. Check output above for results.\n";
            std::cout << "Note: Some tests may show warnings if Roblox is not running\n";
            std::cout << "or if elevated privileges are required.\n";
            
        } catch (const std::exception& e) {
            std::cout << "✗ Test failed with exception: " << e.what() << "\n";
        }
    }
};

int main() {
    RobloxInjectorTests tests;
    tests.runAllTests();
    return 0;
}
