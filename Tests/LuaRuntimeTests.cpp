#include <iostream>
#include <cassert>
#include "../Core/LuaRuntime/SandboxedLuaEngine.h"

using namespace RobloxResearch::LuaRuntime;

bool testLuaEngineInitialization() {
    std::cout << "Testing Lua Engine Initialization..." << std::endl;
    
    SandboxedLuaEngine engine;
    
    if (!engine.initialize()) {
        std::cout << "❌ Failed to initialize Lua engine" << std::endl;
        return false;
    }
    
    std::cout << "✅ Lua engine initialized successfully" << std::endl;
    return true;
}

bool testSafeScriptExecution() {
    std::cout << "Testing Safe Script Execution..." << std::endl;
    
    SandboxedLuaEngine engine;
    engine.initialize();
    
    // Test basic arithmetic
    std::string script = "return 2 + 2";
    auto result = engine.executeScript(script);
    
    if (!result.success) {
        std::cout << "❌ Safe script execution failed: " << result.errorMessage << std::endl;
        return false;
    }
    
    std::cout << "✅ Safe script executed successfully" << std::endl;
    return true;
}

bool testSecurityBoundaryEnforcement() {
    std::cout << "Testing Security Boundary Enforcement..." << std::endl;
    
    SandboxedLuaEngine engine;
    // engine.initialize(); // Method not available
    
    // Test potentially unsafe operations
    std::vector<std::string> unsafeScripts = {
        "os.execute('echo test')",
        "io.open('/etc/passwd', 'r')",
        "require('socket')"
    };
    
    for (const auto& script : unsafeScripts) {
        RobloxResearch::LuaRuntime::SandboxedLuaEngine::ExecutionContext context;
        context.script = script;
        context.timeout = std::chrono::milliseconds(5000);
        context.memoryLimit = 10 * 1024 * 1024;
        auto result = engine.executeScript(context);
        if (result.success) {
            std::cout << "❌ Unsafe script was allowed: " << script << std::endl;
            return false;
        }
    }
    
    std::cout << "✅ Security boundaries properly enforced" << std::endl;
    return true;
}

bool testSecurityTestSuite() {
    std::cout << "Testing Security Test Suite..." << std::endl;
    
    SandboxedLuaEngine engine;
    // engine.initialize(); // Method not available
    
    auto report = engine.runSecurityTestSuite();
    
    if (!report.overallSecurityValid) {
        std::cout << "❌ Security test suite failed" << std::endl;
        return false;
    }
    
    std::cout << "✅ Security test suite passed (" << report.testResults.size() << " tests)" << std::endl;
    return true;
}

bool runLuaRuntimeTests() {
    std::cout << "=== Lua Runtime Tests ===" << std::endl;
    
    bool allPassed = true;
    
    if (!testLuaEngineInitialization()) {
        allPassed = false;
    }
    
    if (!testSafeScriptExecution()) {
        allPassed = false;
    }
    
    if (!testSecurityBoundaryEnforcement()) {
        allPassed = false;
    }
    
    if (!testSecurityTestSuite()) {
        allPassed = false;
    }
    
    return allPassed;
}
