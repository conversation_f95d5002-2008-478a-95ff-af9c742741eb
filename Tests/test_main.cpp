#include "ComprehensiveTestSuite.h"
#include <iostream>
#include <iomanip>

using namespace RobloxResearch::Testing;

void printHeader() {
    std::cout << "========================================\n";
    std::cout << "ROBLOX EXECUTOR SECURITY RESEARCH\n";
    std::cout << "COMPREHENSIVE TEST SUITE\n";
    std::cout << "========================================\n";
    std::cout << "Educational Security Research Framework\n";
    std::cout << "Version 1.0.0\n";
    std::cout << "========================================\n\n";
}

void printTestReport(const ComprehensiveTestSuite::TestSuiteReport& report) {
    std::cout << "=== TEST EXECUTION SUMMARY ===\n\n";
    
    // Overall statistics
    std::cout << "Total Tests: " << report.testReports.size() << "\n";
    std::cout << "Passed: " << report.overallResults.at(ComprehensiveTestSuite::TestResult::PASS) << "\n";
    std::cout << "Failed: " << report.overallResults.at(ComprehensiveTestSuite::TestResult::FAIL) << "\n";
    std::cout << "Warnings: " << report.overallResults.at(ComprehensiveTestSuite::TestResult::WARNING) << "\n";
    std::cout << "Skipped: " << report.overallResults.at(ComprehensiveTestSuite::TestResult::SKIP) << "\n";
    std::cout << "Success Rate: " << std::fixed << std::setprecision(1) << report.successRate << "%\n";
    std::cout << "Execution Time: " << report.totalExecutionTime.count() << "ms\n\n";
    
    // Category breakdown
    std::cout << "=== CATEGORY RESULTS ===\n";
    for (const auto& categoryPair : report.categoryResults) {
        std::string categoryName;
        switch (categoryPair.first) {
            case ComprehensiveTestSuite::TestCategory::SECURITY_VALIDATION:
                categoryName = "Security Validation";
                break;
            case ComprehensiveTestSuite::TestCategory::LUA_RUNTIME:
                categoryName = "Lua Runtime";
                break;
            case ComprehensiveTestSuite::TestCategory::API_SIMULATION:
                categoryName = "API Simulation";
                break;
            case ComprehensiveTestSuite::TestCategory::PROCESS_ANALYSIS:
                categoryName = "Process Analysis";
                break;
            case ComprehensiveTestSuite::TestCategory::INTEGRATION_FRAMEWORK:
                categoryName = "Integration Framework";
                break;
            case ComprehensiveTestSuite::TestCategory::UI_COMPONENTS:
                categoryName = "UI Components";
                break;
            case ComprehensiveTestSuite::TestCategory::EDUCATIONAL_FEATURES:
                categoryName = "Educational Features";
                break;
            case ComprehensiveTestSuite::TestCategory::COMPLIANCE_VALIDATION:
                categoryName = "Compliance Validation";
                break;
        }
        std::cout << categoryName << ": " << categoryPair.second << " tests\n";
    }
    std::cout << "\n";
    
    // Critical failures
    if (!report.criticalFailures.empty()) {
        std::cout << "=== CRITICAL FAILURES ===\n";
        for (const auto& failure : report.criticalFailures) {
            std::cout << "❌ " << failure << "\n";
        }
        std::cout << "\n";
    }
    
    // Security warnings
    if (!report.securityWarnings.empty()) {
        std::cout << "=== SECURITY WARNINGS ===\n";
        for (const auto& warning : report.securityWarnings) {
            std::cout << "⚠️  " << warning << "\n";
        }
        std::cout << "\n";
    }
    
    // Educational notes
    std::cout << "=== EDUCATIONAL NOTES ===\n";
    for (const auto& note : report.educationalNotes) {
        std::cout << "📚 " << note << "\n";
    }
    std::cout << "\n";
    
    // Overall result
    std::cout << "=== OVERALL RESULT ===\n";
    if (report.overallSuccess) {
        std::cout << "✅ TEST SUITE PASSED\n";
        std::cout << "Framework is ready for educational use.\n";
    } else {
        std::cout << "❌ TEST SUITE FAILED\n";
        std::cout << "Critical issues detected. Review failures before use.\n";
    }
    std::cout << "\n";
}

void printDetailedTestResults(const ComprehensiveTestSuite::TestSuiteReport& report) {
    std::cout << "=== DETAILED TEST RESULTS ===\n\n";
    
    for (const auto& testReport : report.testReports) {
        std::string resultSymbol;
        switch (testReport.result) {
            case ComprehensiveTestSuite::TestResult::PASS:
                resultSymbol = "✅";
                break;
            case ComprehensiveTestSuite::TestResult::FAIL:
                resultSymbol = "❌";
                break;
            case ComprehensiveTestSuite::TestResult::WARNING:
                resultSymbol = "⚠️ ";
                break;
            case ComprehensiveTestSuite::TestResult::SKIP:
                resultSymbol = "⏭️ ";
                break;
        }
        
        std::cout << resultSymbol << " " << testReport.name;
        std::cout << " (" << testReport.executionTime.count() << "ms)\n";
        
        if (!testReport.output.empty()) {
            std::cout << "   Output: " << testReport.output << "\n";
        }
        
        if (!testReport.errorMessage.empty()) {
            std::cout << "   Error: " << testReport.errorMessage << "\n";
        }
        
        std::cout << "\n";
    }
}

int main(int argc, char* argv[]) {
    printHeader();
    
    // Parse command line arguments
    bool verbose = false;
    bool securityOnly = false;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--verbose" || arg == "-v") {
            verbose = true;
        } else if (arg == "--security-only" || arg == "-s") {
            securityOnly = true;
        } else if (arg == "--help" || arg == "-h") {
            std::cout << "Usage: " << argv[0] << " [options]\n";
            std::cout << "Options:\n";
            std::cout << "  --verbose, -v      Show detailed test results\n";
            std::cout << "  --security-only, -s Run only security validation tests\n";
            std::cout << "  --help, -h         Show this help message\n";
            return 0;
        }
    }
    
    try {
        // Initialize test suite
        ComprehensiveTestSuite testSuite;
        
        std::cout << "Initializing test suite...\n";
        if (!testSuite.initialize()) {
            std::cerr << "❌ Failed to initialize test suite\n";
            return 1;
        }
        std::cout << "✅ Test suite initialized successfully\n\n";
        
        // Run tests
        ComprehensiveTestSuite::TestSuiteReport report;
        
        if (securityOnly) {
            std::cout << "Running security validation tests only...\n\n";
            auto securityTests = testSuite.runCategoryTests(
                ComprehensiveTestSuite::TestCategory::SECURITY_VALIDATION);
            
            // Convert to suite report format
            report.testReports = securityTests;
            report.suiteStartTime = std::chrono::system_clock::now();
            report.totalExecutionTime = std::chrono::milliseconds(0);
            
            for (const auto& test : securityTests) {
                report.totalExecutionTime += test.executionTime;
                report.overallResults[test.result]++;
            }
            
            size_t totalTests = securityTests.size();
            if (totalTests > 0) {
                report.successRate = static_cast<double>(
                    report.overallResults[ComprehensiveTestSuite::TestResult::PASS]) / totalTests * 100.0;
            }
            
            report.overallSuccess = report.criticalFailures.empty();
            report.educationalNotes = {
                "Security validation tests completed",
                "Framework security boundaries validated"
            };
        } else {
            std::cout << "Running comprehensive test suite...\n\n";
            report = testSuite.runAllTests();
        }
        
        // Print results
        printTestReport(report);
        
        if (verbose) {
            printDetailedTestResults(report);
        }
        
        // Run security compliance validation
        std::cout << "=== SECURITY COMPLIANCE VALIDATION ===\n";
        std::string complianceReport = testSuite.validateSecurityCompliance();
        std::cout << complianceReport << "\n";
        
        // Run educational demonstrations
        std::cout << "=== EDUCATIONAL DEMONSTRATIONS ===\n";
        std::string educationalReport = testSuite.runEducationalDemonstrations();
        std::cout << educationalReport << "\n";
        
        // Final summary
        std::cout << "========================================\n";
        std::cout << "TEST EXECUTION COMPLETED\n";
        std::cout << "========================================\n";
        
        if (report.overallSuccess) {
            std::cout << "🎉 All tests passed! Framework is ready for educational use.\n";
            std::cout << "The security research framework demonstrates comprehensive\n";
            std::cout << "educational capabilities while maintaining strict security\n";
            std::cout << "boundaries and respecting macOS system integrity.\n";
            return 0;
        } else {
            std::cout << "⚠️  Some tests failed. Please review the results above.\n";
            std::cout << "Address any critical failures before using the framework.\n";
            return 1;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test execution failed with exception: " << e.what() << "\n";
        return 1;
    } catch (...) {
        std::cerr << "❌ Test execution failed with unknown exception\n";
        return 1;
    }
}
