#include <iostream>
#include <cassert>
#include "../Core/ProcessMonitor/ProcessAnalyzer.h"
#include "../Core/ProcessMonitor/SecurityBoundaryChecker.h"

using namespace RobloxResearch::ProcessMonitor;

bool testProcessAnalyzer() {
    std::cout << "Testing ProcessAnalyzer..." << std::endl;
    
    ProcessAnalyzer analyzer;
    
    // Test process enumeration
    auto processes = analyzer.getRunningProcesses();
    if (processes.empty()) {
        std::cout << "❌ No processes found" << std::endl;
        return false;
    }
    
    std::cout << "✅ Found " << processes.size() << " processes" << std::endl;
    
    // Test security constraint analysis
    pid_t currentPid = getpid();
    auto constraints = analyzer.analyzeSecurityConstraints(currentPid);
    
    std::cout << "✅ Security constraints analyzed for PID " << currentPid << std::endl;
    
    return true;
}

bool testSecurityBoundaryChecker() {
    std::cout << "Testing SecurityBoundaryChecker..." << std::endl;
    
    SecurityBoundaryChecker checker;
    
    // Test SIP validation
    bool sipEnabled = checker.validateSIPStatus();
    std::cout << "✅ SIP Status: " << (sipEnabled ? "Enabled" : "Disabled") << std::endl;
    
    // Test process boundary analysis
    pid_t currentPid = getpid();
    auto boundary = checker.analyzeProcessBoundaries(currentPid);
    
    std::cout << "✅ Process boundary analyzed for PID " << currentPid << std::endl;
    
    // Test security violations
    auto violations = checker.getSecurityViolations(currentPid);
    std::cout << "✅ Found " << violations.size() << " security violations" << std::endl;
    
    return true;
}

bool runProcessMonitorTests() {
    std::cout << "=== Process Monitor Tests ===" << std::endl;
    
    bool allPassed = true;
    
    if (!testProcessAnalyzer()) {
        allPassed = false;
    }
    
    if (!testSecurityBoundaryChecker()) {
        allPassed = false;
    }
    
    return allPassed;
}
