#include <iostream>
#include <cassert>
#include "../Core/SecurityAnalysis/MemoryAnalyzer.h"
#include "../Core/SecurityAnalysis/CodeSigningAnalyzer.h"
#include "../Core/Compliance/SecurityComplianceValidator.h"

using namespace RobloxResearch;

bool testMemoryAnalysis() {
    std::cout << "Testing Memory Analysis..." << std::endl;
    
    SecurityAnalysis::MemoryAnalyzer analyzer;
    
    // Test memory analysis report
    auto report = RobloxResearch::SecurityAnalysis::MemoryProtectionResearch::generateMemorySecurityReport();
    if (report.empty()) {
        std::cout << "❌ Memory analysis report generation failed" << std::endl;
        return false;
    }

    std::cout << "✅ Memory analysis report generated" << std::endl;
    
    // Test memory protection analysis
    auto protectionReport = SecurityAnalysis::MemoryProtectionResearch::generateMemorySecurityReport();
    if (protectionReport.empty()) {
        std::cout << "❌ Memory protection analysis failed" << std::endl;
        return false;
    }
    
    std::cout << "✅ Memory protection analysis completed" << std::endl;
    return true;
}

bool testCodeSigningAnalysis() {
    std::cout << "Testing Code Signing Analysis..." << std::endl;
    
    SecurityAnalysis::CodeSigningAnalyzer analyzer;
    
    // Test downloader script analysis
    auto downloaderAnalysis = analyzer.analyzeRobloxDownloaderScript();
    if (downloaderAnalysis.securityActions.empty()) {
        std::cout << "❌ Downloader script analysis failed" << std::endl;
        return false;
    }
    
    std::cout << "✅ Downloader script analysis completed" << std::endl;
    
    // Test security research report
    auto researchReport = analyzer.generateSecurityResearchReport();
    if (researchReport.empty()) {
        std::cout << "❌ Security research report generation failed" << std::endl;
        return false;
    }
    
    std::cout << "✅ Security research report generated" << std::endl;
    return true;
}

bool testComplianceValidation() {
    std::cout << "Testing Compliance Validation..." << std::endl;
    
    Compliance::SecurityComplianceValidator validator;
    
    if (!validator.initialize()) {
        std::cout << "❌ Compliance validator initialization failed" << std::endl;
        return false;
    }
    
    std::cout << "✅ Compliance validator initialized" << std::endl;
    
    // Test compliance check
    auto report = validator.runComplianceCheck();
    if (report.totalRules == 0) {
        std::cout << "❌ No compliance rules found" << std::endl;
        return false;
    }
    
    std::cout << "✅ Compliance check completed (" << report.totalRules << " rules)" << std::endl;
    std::cout << "  - Compliant: " << report.compliantRules << std::endl;
    std::cout << "  - Non-compliant: " << report.nonCompliantRules << std::endl;
    std::cout << "  - Score: " << (report.complianceScore * 100) << "%" << std::endl;
    
    return true;
}

bool runSecurityValidationTests() {
    std::cout << "=== Security Validation Tests ===" << std::endl;
    
    bool allPassed = true;
    
    if (!testMemoryAnalysis()) {
        allPassed = false;
    }
    
    if (!testCodeSigningAnalysis()) {
        allPassed = false;
    }
    
    if (!testComplianceValidation()) {
        allPassed = false;
    }
    
    return allPassed;
}
