#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <chrono>

#include "../Core/LuaRuntime/SandboxedLuaEngine.h"
#include "../Core/LuaRuntime/RobloxAPISimulator.h"
#include "../Core/SecurityAnalysis/RobloxApplicationAnalyzer.h"
#include "../Core/ProcessInteraction/EducationalDylibFramework.h"
#include "../Core/Integration/SecureIntegrationFramework.h"

namespace RobloxResearch {
namespace Testing {

/**
 * @brief Comprehensive test suite for the educational security research framework
 */
class ComprehensiveTestSuite {
public:
    enum class TestCategory {
        SECURITY_VALIDATION,
        LUA_RUNTIME,
        API_SIMULATION,
        PROCESS_ANALYSIS,
        INTEGRATION_FRAMEWORK,
        UI_COMPONENTS,
        EDUCATIONAL_FEATURES,
        COMPLIANCE_VALIDATION
    };

    enum class TestResult {
        PASS,
        FAIL,
        SKIP,
        WARNING
    };

    struct TestCase {
        std::string testId;
        std::string name;
        std::string description;
        TestCategory category;
        std::function<TestResult()> testFunction;
        std::vector<std::string> prerequisites;
        std::chrono::milliseconds timeout{30000}; // 30 seconds default
        bool isRequired;
        std::string expectedBehavior;
    };

    struct TestReport {
        std::string testId;
        std::string name;
        TestResult result;
        std::string output;
        std::string errorMessage;
        std::chrono::milliseconds executionTime;
        std::chrono::system_clock::time_point timestamp;
        std::map<std::string, std::string> metadata;
    };

    struct TestSuiteReport {
        std::vector<TestReport> testReports;
        std::map<TestCategory, size_t> categoryResults;
        std::map<TestResult, size_t> overallResults;
        std::vector<std::string> criticalFailures;
        std::vector<std::string> securityWarnings;
        std::vector<std::string> educationalNotes;
        std::chrono::system_clock::time_point suiteStartTime;
        std::chrono::milliseconds totalExecutionTime;
        bool overallSuccess;
        double successRate;
    };

    ComprehensiveTestSuite();
    ~ComprehensiveTestSuite();

    /**
     * @brief Initialize test suite with framework components
     * @return true if initialization successful
     */
    bool initialize();

    /**
     * @brief Run all tests in the suite
     * @return Comprehensive test suite report
     */
    TestSuiteReport runAllTests();

    /**
     * @brief Run tests for specific category
     * @param category Test category to run
     * @return Test reports for the category
     */
    std::vector<TestReport> runCategoryTests(TestCategory category);

    /**
     * @brief Run specific test by ID
     * @param testId Test identifier
     * @return Test report
     */
    TestReport runTest(const std::string& testId);

    /**
     * @brief Get available test categories
     * @return Vector of test categories
     */
    std::vector<TestCategory> getAvailableCategories();

    /**
     * @brief Get tests for specific category
     * @param category Test category
     * @return Vector of test cases
     */
    std::vector<TestCase> getTestsForCategory(TestCategory category);

    /**
     * @brief Validate framework security compliance
     * @return Security compliance report
     */
    std::string validateSecurityCompliance();

    /**
     * @brief Run educational demonstration tests
     * @return Educational test results
     */
    std::string runEducationalDemonstrations();

    /**
     * @brief Generate detailed test documentation
     * @return Test documentation
     */
    std::string generateTestDocumentation();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;

    // Test category implementations
    std::vector<TestCase> createSecurityValidationTests();
    std::vector<TestCase> createLuaRuntimeTests();
    std::vector<TestCase> createAPISimulationTests();
    std::vector<TestCase> createProcessAnalysisTests();
    std::vector<TestCase> createIntegrationFrameworkTests();
    std::vector<TestCase> createUIComponentTests();
    std::vector<TestCase> createEducationalFeatureTests();
    std::vector<TestCase> createComplianceValidationTests();

    // Individual test implementations
    TestResult testLuaSandboxSecurity();
    TestResult testAPISimulationAccuracy();
    TestResult testProcessAnalysisSafety();
    TestResult testIntegrationSecurity();
    TestResult testEducationalBoundaries();
    TestResult testSecurityEventLogging();
    TestResult testMemoryLimits();
    TestResult testTimeoutHandling();
    TestResult testCodeSigningValidation();
    TestResult testSIPCompliance();
    TestResult testSandboxRestrictions();
    TestResult testUISecurityFeatures();
    TestResult testScriptValidation();
    TestResult testDylibFrameworkSafety();
    TestResult testRobloxAnalyzerAccuracy();

    // Helper methods
    bool executeTestWithTimeout(const std::function<TestResult()>& test, 
                               std::chrono::milliseconds timeout,
                               TestResult& result);
    std::string formatTestReport(const TestReport& report);
    std::string formatSuiteReport(const TestSuiteReport& report);
    void logTestExecution(const std::string& testId, TestResult result);
    bool checkPrerequisites(const std::vector<std::string>& prerequisites);
};

/**
 * @brief Security validation test runner
 */
class SecurityValidationRunner {
public:
    struct SecurityTest {
        std::string testName;
        std::string description;
        std::function<bool()> validator;
        std::string expectedOutcome;
        std::vector<std::string> securityImplications;
    };

    struct SecurityReport {
        std::vector<SecurityTest> passedTests;
        std::vector<SecurityTest> failedTests;
        std::vector<std::string> criticalIssues;
        std::vector<std::string> recommendations;
        bool overallSecurityValid;
        std::string securityLevel;
    };

    /**
     * @brief Run comprehensive security validation
     * @return Security validation report
     */
    static SecurityReport runSecurityValidation();

    /**
     * @brief Test sandbox isolation
     * @return true if sandbox is properly isolated
     */
    static bool testSandboxIsolation();

    /**
     * @brief Test dangerous function blocking
     * @return true if dangerous functions are blocked
     */
    static bool testDangerousFunctionBlocking();

    /**
     * @brief Test memory protection
     * @return true if memory is properly protected
     */
    static bool testMemoryProtection();

    /**
     * @brief Test process isolation
     * @return true if processes are properly isolated
     */
    static bool testProcessIsolation();

    /**
     * @brief Test educational boundaries
     * @return true if educational boundaries are maintained
     */
    static bool testEducationalBoundaries();

private:
    static std::vector<SecurityTest> createSecurityTests();
    static bool validateSystemIntegrity();
    static bool validateSecurityConstraints();
};

/**
 * @brief Educational feature validator
 */
class EducationalFeatureValidator {
public:
    struct EducationalTest {
        std::string featureName;
        std::string description;
        std::function<bool()> validator;
        std::vector<std::string> learningObjectives;
        std::string educationalValue;
    };

    struct EducationalReport {
        std::vector<EducationalTest> validatedFeatures;
        std::vector<std::string> educationalNotes;
        std::vector<std::string> improvementSuggestions;
        bool educationalGoalsMet;
        std::string overallAssessment;
    };

    /**
     * @brief Validate educational features
     * @return Educational validation report
     */
    static EducationalReport validateEducationalFeatures();

    /**
     * @brief Test API simulation educational value
     * @return true if API simulation provides educational value
     */
    static bool testAPISimulationEducationalValue();

    /**
     * @brief Test security demonstration effectiveness
     * @return true if security demonstrations are effective
     */
    static bool testSecurityDemonstrationEffectiveness();

    /**
     * @brief Test learning objective achievement
     * @return true if learning objectives are achieved
     */
    static bool testLearningObjectiveAchievement();

private:
    static std::vector<EducationalTest> createEducationalTests();
    static bool validateLearningOutcomes();
    static bool assessEducationalImpact();
};

/**
 * @brief Performance and resource usage tester
 */
class PerformanceTestRunner {
public:
    struct PerformanceMetrics {
        std::chrono::milliseconds executionTime;
        size_t memoryUsage;
        size_t cpuUsage;
        size_t diskUsage;
        size_t networkUsage;
        std::map<std::string, double> customMetrics;
    };

    struct PerformanceReport {
        std::map<std::string, PerformanceMetrics> testMetrics;
        std::vector<std::string> performanceIssues;
        std::vector<std::string> optimizationSuggestions;
        bool performanceAcceptable;
        std::string overallAssessment;
    };

    /**
     * @brief Run performance tests
     * @return Performance test report
     */
    static PerformanceReport runPerformanceTests();

    /**
     * @brief Test Lua runtime performance
     * @return Performance metrics
     */
    static PerformanceMetrics testLuaRuntimePerformance();

    /**
     * @brief Test UI responsiveness
     * @return Performance metrics
     */
    static PerformanceMetrics testUIResponsiveness();

    /**
     * @brief Test memory usage patterns
     * @return Performance metrics
     */
    static PerformanceMetrics testMemoryUsage();

private:
    static PerformanceMetrics measureResourceUsage(const std::function<void()>& operation);
    static bool isPerformanceAcceptable(const PerformanceMetrics& metrics);
};

} // namespace Testing
} // namespace RobloxResearch
