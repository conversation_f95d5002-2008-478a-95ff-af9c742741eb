#include <iostream>

// Test function declarations
bool runProcessMonitorTests();
bool runLuaRuntimeTests();
bool runSecurityValidationTests();

int main() {
    std::cout << "Roblox Executor Security Research Framework" << std::endl;
    std::cout << "Test Suite" << std::endl;
    std::cout << "=========================================" << std::endl;
    std::cout << std::endl;

    bool allTestsPassed = true;

    // Run Process Monitor Tests
    if (!runProcessMonitorTests()) {
        allTestsPassed = false;
    }
    std::cout << std::endl;

    // Run Lua Runtime Tests
    if (!runLuaRuntimeTests()) {
        allTestsPassed = false;
    }
    std::cout << std::endl;

    // Run Security Validation Tests
    if (!runSecurityValidationTests()) {
        allTestsPassed = false;
    }
    std::cout << std::endl;

    // Final Results
    std::cout << "=========================================" << std::endl;
    if (allTestsPassed) {
        std::cout << "🎉 ALL TESTS PASSED!" << std::endl;
        std::cout << "✅ Framework is ready for use" << std::endl;
        return 0;
    } else {
        std::cout << "❌ SOME TESTS FAILED" << std::endl;
        std::cout << "⚠️  Review failed components" << std::endl;
        return 1;
    }
}


